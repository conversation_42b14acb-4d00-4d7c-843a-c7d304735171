"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const bcrypt = __importStar(require("bcryptjs"));
const user_schema_1 = require("./schemas/user.schema");
let UsersService = class UsersService {
    constructor(userModel) {
        this.userModel = userModel;
    }
    async create(createUserData) {
        const { username, password, nom, prenom, email, telephone, role, statut } = createUserData;
        // Vérifier si le nom d'utilisateur existe déjà
        const existingUser = await this.userModel.findOne({ username });
        if (existingUser) {
            throw new common_1.ConflictException('Ce nom d\'utilisateur est déjà utilisé.');
        }
        // Vérifier si l'email existe déjà
        const existingEmail = await this.userModel.findOne({ email });
        if (existingEmail) {
            throw new common_1.ConflictException('Cette adresse email est déjà utilisée.');
        }
        const hashedPassword = await bcrypt.hash(password, 10);
        const createdUser = new this.userModel({
            username,
            password: hashedPassword,
            nom,
            prenom,
            email,
            telephone,
            role,
            statut: statut || 'actif', // Statut par défaut
        });
        return createdUser.save();
    }
    async findByUsername(username) {
        return this.userModel.findOne({ username }).exec();
    }
    async findById(id) {
        return this.userModel.findById(id).exec();
    }
    async getUserId(username) {
        const user = await this.userModel.findOne({ username }).exec();
        if (user) {
            return user._id;
        }
    }
    async validateUser(username, pass) {
        const user = await this.findByUsername(username);
        if (!user)
            return null;
        const isPasswordValid = await bcrypt.compare(pass, user.password);
        if (!isPasswordValid)
            return null;
        return user;
    }
    async findAll(role, statut, search) {
        const filter = {};
        // Filtrer par rôle si spécifié
        if (role) {
            filter.role = role;
        }
        // Filtrer par statut si spécifié
        if (statut) {
            filter.statut = statut;
        }
        // Recherche textuelle si spécifiée
        if (search) {
            filter.$or = [
                { nom: { $regex: search, $options: 'i' } },
                { prenom: { $regex: search, $options: 'i' } },
                { email: { $regex: search, $options: 'i' } },
                { username: { $regex: search, $options: 'i' } },
            ];
        }
        return this.userModel.find(filter).exec();
    }
    async findOne(id) {
        return this.userModel.findById(id).exec();
    }
    async update(id, updateUserData) {
        // Vérifier si l'utilisateur existe
        const existingUser = await this.userModel.findById(id);
        if (!existingUser) {
            throw new common_1.ConflictException('Utilisateur non trouvé.');
        }
        // Vérifier l'unicité du nom d'utilisateur si modifié
        if (updateUserData.username && updateUserData.username !== existingUser.username) {
            const userWithSameUsername = await this.userModel.findOne({ username: updateUserData.username });
            if (userWithSameUsername) {
                throw new common_1.ConflictException('Ce nom d\'utilisateur est déjà utilisé.');
            }
        }
        // Vérifier l'unicité de l'email si modifié
        if (updateUserData.email && updateUserData.email !== existingUser.email) {
            const userWithSameEmail = await this.userModel.findOne({ email: updateUserData.email });
            if (userWithSameEmail) {
                throw new common_1.ConflictException('Cette adresse email est déjà utilisée.');
            }
        }
        let data = { ...updateUserData };
        // Hasher le mot de passe s'il est fourni
        if (updateUserData.password) {
            data.password = await bcrypt.hash(updateUserData.password, 10);
        }
        return this.userModel.findByIdAndUpdate(id, data, { new: true }).exec();
    }
    async remove(id) {
        return this.userModel.findByIdAndDelete(id).exec();
    }
    // Méthodes spécialisées pour la gestion des membres
    async updateStatut(id, statut) {
        return this.userModel.findByIdAndUpdate(id, { statut }, { new: true }).exec();
    }
    async findByRole(role) {
        return this.userModel.find({ role }).exec();
    }
    async findByStatut(statut) {
        return this.userModel.find({ statut }).exec();
    }
    async getStats() {
        const totalUsers = await this.userModel.countDocuments();
        // Statistiques par rôle
        const statsByRole = await this.userModel.aggregate([
            {
                $group: {
                    _id: '$role',
                    count: { $sum: 1 }
                }
            }
        ]);
        // Statistiques par statut
        const statsByStatus = await this.userModel.aggregate([
            {
                $group: {
                    _id: '$statut',
                    count: { $sum: 1 }
                }
            }
        ]);
        // Membres actifs par rôle
        const activeByRole = await this.userModel.aggregate([
            {
                $match: { statut: user_schema_1.UserStatus.ACTIF }
            },
            {
                $group: {
                    _id: '$role',
                    count: { $sum: 1 }
                }
            }
        ]);
        return {
            total: totalUsers,
            byRole: statsByRole.reduce((acc, item) => {
                acc[item._id] = item.count;
                return acc;
            }, {}),
            byStatus: statsByStatus.reduce((acc, item) => {
                acc[item._id] = item.count;
                return acc;
            }, {}),
            activeByRole: activeByRole.reduce((acc, item) => {
                acc[item._id] = item.count;
                return acc;
            }, {}),
        };
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(user_schema_1.User.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], UsersService);
