# =================================================================
# Authentication Configuration (Clerk)
# =================================================================
# IMPORTANT: This template supports Clerk's keyless mode!
# You can start using the app immediately without any configuration.
# When you're ready to claim your application, simply click the Clerk
# popup at the bottom of the screen to get your API keys.

# Required: Clerk API Keys (Leave empty for keyless mode)

NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=
CLERK_SECRET_KEY=


# Authentication Redirect URLs
# These control where users are directed after authentication actions

NEXT_PUBLIC_CLERK_SIGN_IN_URL="/auth/sign-in"
NEXT_PUBLIC_CLERK_SIGN_UP_URL="/auth/sign-up"
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL="/dashboard/overview"
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL="/dashboard/overview"


# =================================================================
# Error Tracking Configuration (Sentry)
# =================================================================
# To set up Sentry error tracking:
# 1. Create an account at https://sentry.io
# 2. Create a new project for Next.js
# 3. Follow the setup instructions below

# Step 1: Sentry DSN (Required)
# Found at: Settings > Projects > [Your Project] > Client Keys (DSN)

NEXT_PUBLIC_SENTRY_DSN=    #Example: https://****@****.ingest.sentry.io/****


# Step 2: Organization & Project Details
# Found at: Settings > Organization > General Settings

NEXT_PUBLIC_SENTRY_ORG=  # Example: acme-corp
NEXT_PUBLIC_SENTRY_PROJECT=  # Example: nextjs-dashboard


# Step 3: Sentry Auth Token

# Sentry can automatically provide readable stack traces for errors using source maps, requiring a Sentry auth token.
# More info: https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/#step-4-add-readable-stack-traces-with-source-maps-optional

SENTRY_AUTH_TOKEN=    #Example: sntrys_************************************


# Step 4: Environment Control (Optional)
# Set to 'true' to disable Sentry in development

NEXT_PUBLIC_SENTRY_DISABLED= "false"


# =================================================================
# Important Notes:
# =================================================================
# 1. Rename this file to '.env' for local development
# 2. Never commit the actual '.env' file to version control
# 3. Make sure to replace all placeholder values with real ones
# 4. Keep your secret keys private and never share them
