{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/app/page.tsx"], "sourcesContent": ["export default function Home() {\n\treturn null;\n}\n"], "names": [], "mappings": ";;;;AAAe,SAAS;IACvB,OAAO;AACR", "debugId": null}}]}