lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    devDependencies:
      '@biomejs/biome':
        specifier: 2.2.3
        version: 2.2.3

packages:

  '@biomejs/biome@2.2.3':
    resolution: {integrity: sha512-9w0uMTvPrIdvUrxazZ42Ib7t8Y2yoGLKLdNne93RLICmaHw7mcLv4PPb5LvZLJF3141gQHiCColOh/v6VWlWmg==}
    engines: {node: '>=14.21.3'}
    hasBin: true

  '@biomejs/cli-darwin-arm64@2.2.3':
    resolution: {integrity: sha512-OrqQVBpadB5eqzinXN4+Q6honBz+tTlKVCsbEuEpljK8ASSItzIRZUA02mTikl3H/1nO2BMPFiJ0nkEZNy3B1w==}
    engines: {node: '>=14.21.3'}
    cpu: [arm64]
    os: [darwin]

  '@biomejs/cli-darwin-x64@2.2.3':
    resolution: {integrity: sha512-OCdBpb1TmyfsTgBAM1kPMXyYKTohQ48WpiN9tkt9xvU6gKVKHY4oVwteBebiOqyfyzCNaSiuKIPjmHjUZ2ZNMg==}
    engines: {node: '>=14.21.3'}
    cpu: [x64]
    os: [darwin]

  '@biomejs/cli-linux-arm64-musl@2.2.3':
    resolution: {integrity: sha512-q3w9jJ6JFPZPeqyvwwPeaiS/6NEszZ+pXKF+IczNo8Xj6fsii45a4gEEicKyKIytalV+s829ACZujQlXAiVLBQ==}
    engines: {node: '>=14.21.3'}
    cpu: [arm64]
    os: [linux]

  '@biomejs/cli-linux-arm64@2.2.3':
    resolution: {integrity: sha512-g/Uta2DqYpECxG+vUmTAmUKlVhnGEcY7DXWgKP8ruLRa8Si1QHsWknPY3B/wCo0KgYiFIOAZ9hjsHfNb9L85+g==}
    engines: {node: '>=14.21.3'}
    cpu: [arm64]
    os: [linux]

  '@biomejs/cli-linux-x64-musl@2.2.3':
    resolution: {integrity: sha512-y76Dn4vkP1sMRGPFlNc+OTETBhGPJ90jY3il6jAfur8XWrYBQV3swZ1Jo0R2g+JpOeeoA0cOwM7mJG6svDz79w==}
    engines: {node: '>=14.21.3'}
    cpu: [x64]
    os: [linux]

  '@biomejs/cli-linux-x64@2.2.3':
    resolution: {integrity: sha512-LEtyYL1fJsvw35CxrbQ0gZoxOG3oZsAjzfRdvRBRHxOpQ91Q5doRVjvWW/wepgSdgk5hlaNzfeqpyGmfSD0Eyw==}
    engines: {node: '>=14.21.3'}
    cpu: [x64]
    os: [linux]

  '@biomejs/cli-win32-arm64@2.2.3':
    resolution: {integrity: sha512-Ms9zFYzjcJK7LV+AOMYnjN3pV3xL8Prxf9aWdDVL74onLn5kcvZ1ZMQswE5XHtnd/r/0bnUd928Rpbs14BzVmA==}
    engines: {node: '>=14.21.3'}
    cpu: [arm64]
    os: [win32]

  '@biomejs/cli-win32-x64@2.2.3':
    resolution: {integrity: sha512-gvCpewE7mBwBIpqk1YrUqNR4mCiyJm6UI3YWQQXkedSSEwzRdodRpaKhbdbHw1/hmTWOVXQ+Eih5Qctf4TCVOQ==}
    engines: {node: '>=14.21.3'}
    cpu: [x64]
    os: [win32]

snapshots:

  '@biomejs/biome@2.2.3':
    optionalDependencies:
      '@biomejs/cli-darwin-arm64': 2.2.3
      '@biomejs/cli-darwin-x64': 2.2.3
      '@biomejs/cli-linux-arm64': 2.2.3
      '@biomejs/cli-linux-arm64-musl': 2.2.3
      '@biomejs/cli-linux-x64': 2.2.3
      '@biomejs/cli-linux-x64-musl': 2.2.3
      '@biomejs/cli-win32-arm64': 2.2.3
      '@biomejs/cli-win32-x64': 2.2.3

  '@biomejs/cli-darwin-arm64@2.2.3':
    optional: true

  '@biomejs/cli-darwin-x64@2.2.3':
    optional: true

  '@biomejs/cli-linux-arm64-musl@2.2.3':
    optional: true

  '@biomejs/cli-linux-arm64@2.2.3':
    optional: true

  '@biomejs/cli-linux-x64-musl@2.2.3':
    optional: true

  '@biomejs/cli-linux-x64@2.2.3':
    optional: true

  '@biomejs/cli-win32-arm64@2.2.3':
    optional: true

  '@biomejs/cli-win32-x64@2.2.3':
    optional: true
