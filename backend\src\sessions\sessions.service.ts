import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model, Types } from 'mongoose';
import { Session, SessionDocument } from './schemas/session.schema';
import { CreateSessionDto } from './dto/create-session.dto';
import { UpdateSessionDto } from './dto/update-session.dto';
import { Reunion, ReunionDocument } from '../reunions/schemas/reunion.schema';

@Injectable()
export class SessionsService {
  constructor(
    @InjectModel(Session.name) private sessionModel: Model<SessionDocument>,
    @InjectModel(Reunion.name) private reunionModel: Model<ReunionDocument>,
  ) {}

  private generateSundays(start: Date, end: Date): Date[] {
    if (end < start) throw new BadRequestException('dateFin doit être >= dateDebut');
    // Find first Sunday on/after start
    const result: Date[] = [];
    const date = new Date(start);
    const day = date.getDay(); // 0=Sunday
    const diff = (7 - day) % 7; // days until Sunday
    date.setDate(date.getDate() + diff);
    while (date <= end) {
      result.push(new Date(date));
      date.setDate(date.getDate() + 7);
    }
    return result;
  }

  async create(createDto: CreateSessionDto, createdBy: string): Promise<Session> {
    const { annee, dateDebut, dateFin, partFixe } = createDto;
    const start = new Date(dateDebut);
    const end = new Date(dateFin);

    const session = await this.sessionModel.create({
      annee,
      dateDebut: start,
      dateFin: end,
      partFixe,
      createdBy: new Types.ObjectId(createdBy),
    });

    const sundays = this.generateSundays(start, end);
    if (sundays.length === 0) {
      await session.deleteOne();
      throw new BadRequestException('Aucune réunion générée (vérifiez la période)');
    }

    // Create reunions for each Sunday
    const reunionsToCreate = sundays.map((d) => ({
      dateReunion: d,
      sessionId: session._id,
    }));
    await this.reunionModel.insertMany(reunionsToCreate);

    return session.toObject();
  }

  async findAll(): Promise<Session[]> {
    return this.sessionModel.find().sort({ dateDebut: 1 }).lean();
  }

  async findOne(id: string): Promise<Session | null> {
    if (!Types.ObjectId.isValid(id)) throw new NotFoundException('Session introuvable');
    return this.sessionModel.findById(id).lean();
  }

  async update(id: string, dto: UpdateSessionDto): Promise<Session | null> {
    if (!Types.ObjectId.isValid(id)) throw new NotFoundException('Session introuvable');
    const updated = await this.sessionModel.findByIdAndUpdate(
      id,
      {
        $set: {
          ...('annee' in dto ? { annee: dto.annee } : {}),
          ...('dateDebut' in dto ? { dateDebut: new Date(dto.dateDebut!) } : {}),
          ...('dateFin' in dto ? { dateFin: new Date(dto.dateFin!) } : {}),
          ...('partFixe' in dto ? { partFixe: dto.partFixe } : {}),
        },
      },
      { new: true },
    );
    return updated?.toObject() ?? null;
  }

  async remove(id: string): Promise<void> {
    if (!Types.ObjectId.isValid(id)) throw new NotFoundException('Session introuvable');
    // Delete reunions of session, then session
    await this.reunionModel.deleteMany({ sessionId: new Types.ObjectId(id) });
    await this.sessionModel.findByIdAndDelete(id);
  }
}