import {
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
} from "class-validator";
import { ApiProperty } from "@nestjs/swagger";
import { UserRole, UserStatus } from "../schemas/user.schema";

export class CreateUserDto {
	@ApiProperty({ example: "john_doe", description: "Nom d'utilisateur unique" })
	@IsString()
	@IsNotEmpty()
	readonly username!: string;

	@ApiProperty({
		example: "motdepasse123",
		description: "Mot de passe (minimum 6 caractères)",
	})
	@IsString()
	@MinLength(6)
	readonly password!: string;

	@ApiProperty({ example: "Doe", description: "Nom de famille" })
	@IsString()
	@IsNotEmpty()
	readonly nom!: string;

	@ApiProperty({ example: "John", description: "Prénom" })
	@IsString()
	@IsNotEmpty()
	readonly prenom!: string;

	@ApiProperty({
		example: "<EMAIL>",
		description: "Adresse email unique",
	})
	@IsEmail()
	@IsNotEmpty()
	readonly email!: string;

	@ApiProperty({ example: "+237123456789", description: "Numéro de téléphone" })
	@IsString()
	@IsNotEmpty()
	readonly telephone!: string;

	@ApiProperty({
		enum: UserRole,
		example: UserRole.MEMBRE,
		description: "Rôle de l'utilisateur",
	})
	@IsEnum(UserRole)
	readonly role!: UserRole;

	@ApiProperty({
		enum: UserStatus,
		example: UserStatus.ACTIF,
		description: "Statut de l'utilisateur",
		required: false,
	})
	@IsOptional()
	@IsEnum(UserStatus)
	readonly statut?: UserStatus;
}
