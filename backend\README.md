# Tontine NestJS Application

This project is a NestJS application designed to manage a tontine system. It includes basic user authentication with JWT and supports multiple user roles.

## Features

- User authentication with JWT
- Role-based access control for different user types:
  - Caissier
  - Contrôleur
  - Sec<PERSON>taire Général
- MongoDB integration for data persistence
- Basic CRUD operations for users and tontines

## Project Structure

```
tontine-nestjs-app
├── src
│   ├── app.module.ts          # Root module of the application
│   ├── main.ts                # Entry point of the application
│   ├── auth                   # Authentication module
│   │   ├── auth.controller.ts  # Handles authentication routes
│   │   ├── auth.module.ts      # Auth module definition
│   │   ├── auth.service.ts     # Auth service for user authentication
│   │   └── jwt.strategy.ts     # JWT strategy for authentication
│   ├── users                  # User management module
│   │   ├── users.controller.ts  # Handles user-related routes
│   │   ├── users.module.ts      # User module definition
│   │   ├── users.service.ts     # User service for CRUD operations
│   │   └── user.schema.ts       # User schema for MongoDB
│   ├── tontine                # Tontine management module
│   │   ├── tontine.controller.ts  # Handles tontine-related routes
│   │   ├── tontine.module.ts      # Tontine module definition
│   │   ├── tontine.service.ts     # Tontine service for CRUD operations
│   │   └── tontine.schema.ts       # Tontine schema for MongoDB
│   └── common                 # Common utilities and enums
│       └── enums
│           └── user-role.enum.ts  # Defines user roles
├── package.json               # NPM dependencies and scripts
├── tsconfig.json              # TypeScript configuration
└── README.md                  # Project documentation
```

## Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/tontine-nestjs-app.git
   ```
2. Navigate to the project directory:
   ```
   cd tontine-nestjs-app
   ```
3. Install the dependencies:
   ```
   npm install
   ```

## Running the Application

To start the application, run the following command:
```
npm run start
```

The application will be running on `http://localhost:3000`.

## Usage

- Use the authentication endpoints to register and log in users.
- Access user management and tontine management functionalities through their respective routes.

## License

This project is licensed under the MIT License.