import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Reunion, ReunionDocument } from './schemas/reunion.schema';
import { UpdateReunionDto } from './dto/update-reunion.dto';

@Injectable()
export class ReunionsService {
  constructor(@InjectModel(Reunion.name) private reunionModel: Model<ReunionDocument>) {}

  async findAll(): Promise<Reunion[]> {
    return this.reunionModel.find().sort({ dateReunion: 1 }).lean();
  }

  async findOne(id: string): Promise<Reunion | null> {
    if (!Types.ObjectId.isValid(id)) throw new NotFoundException('Réunion introuvable');
    return this.reunionModel.findById(id).lean();
  }

  async update(id: string, dto: UpdateReunionDto): Promise<Reunion | null> {
    if (!Types.ObjectId.isValid(id)) throw new NotFoundException('Réunion introuvable');
    // Prevent setting a past date if needed; here just transform
    const update: any = {};
    if (dto.dateReunion) update.dateReunion = new Date(dto.dateReunion);
    if (dto.lieu !== undefined) update.lieu = dto.lieu;
    if (dto.caissePrincipale) update.caissePrincipale = new Types.ObjectId(dto.caissePrincipale);

    const updated = await this.reunionModel.findByIdAndUpdate(
      id,
      { $set: update },
      { new: true },
    );
    return updated?.toObject() ?? null;
  }
}