import { useSession } from 'next-auth/react';
import { apiService } from '@/lib/api';

export function useApi() {
  const { data: session } = useSession();

  const authenticatedRequest = async <T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> => {
    if (!session?.accessToken) {
      throw new Error('Non authentifié');
    }

    return apiService.authenticatedRequest<T>(
      endpoint,
      session.accessToken,
      options
    );
  };

  return {
    // Méthodes d'authentification (pas besoin de token)
    login: apiService.login.bind(apiService),
    register: apiService.register.bind(apiService),
    
    // Méthodes authentifiées
    authenticatedRequest,
    
    // Raccourcis pour les endpoints courants
    getUsers: () => authenticatedRequest<any[]>('/users'),
    getUser: (id: string) => authenticatedRequest<any>(`/users/${id}`),
    createUser: (userData: any) => authenticatedRequest<any>('/users', {
      method: 'POST',
      body: JSON.stringify(userData),
    }),
    updateUser: (id: string, userData: any) => authenticatedRequest<any>(`/users/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(userData),
    }),
    deleteUser: (id: string) => authenticatedRequest<any>(`/users/${id}`, {
      method: 'DELETE',
    }),
    
    // Sessions/Exercices
    getSessions: () => authenticatedRequest<any[]>('/sessions'),
    getSession: (id: string) => authenticatedRequest<any>(`/sessions/${id}`),
    createSession: (sessionData: any) => authenticatedRequest<any>('/sessions', {
      method: 'POST',
      body: JSON.stringify(sessionData),
    }),
    
    // Caisses
    getCaisses: () => authenticatedRequest<any[]>('/caisses'),
    getCaisse: (id: string) => authenticatedRequest<any>(`/caisses/${id}`),
    createCaisse: (caisseData: any) => authenticatedRequest<any>('/caisses', {
      method: 'POST',
      body: JSON.stringify(caisseData),
    }),
    
    // Réunions
    getReunions: () => authenticatedRequest<any[]>('/reunions'),
    getReunion: (id: string) => authenticatedRequest<any>(`/reunions/${id}`),
  };
}
