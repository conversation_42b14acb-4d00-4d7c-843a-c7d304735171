{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useSession } from \"next-auth/react\";\nimport { useRouter } from \"next/navigation\";\nimport { useEffect } from \"react\";\n\nexport default function Home() {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (status === \"loading\") return; // Still loading\n\n    if (session) {\n      router.push(\"/dashboard\");\n    } else {\n      router.push(\"/auth/signin\");\n    }\n  }, [session, status, router]);\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center\">\n      <div className=\"text-lg\">Loading...</div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,IAAA,8QAAU;IAC5C,MAAM,SAAS,IAAA,6RAAS;IAExB,IAAA,qTAAS;0BAAC;YACR,IAAI,WAAW,WAAW,QAAQ,gBAAgB;YAElD,IAAI,SAAS;gBACX,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF;yBAAG;QAAC;QAAS;QAAQ;KAAO;IAE5B,qBACE,wUAAC;QAAI,WAAU;kBACb,cAAA,wUAAC;YAAI,WAAU;sBAAU;;;;;;;;;;;AAG/B;GAnBwB;;QACY,8QAAU;QAC7B,6RAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/next%4015.5.2_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}