import { Body, Controller, Delete, Get, Param, Patch, Post, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../common/guards/role.guard';
import { Roles } from '../common/decorators/roles.decorator';
import { UserRole } from '../users/schemas/user.schema';
import { CaissesService } from './caisses.service';
import { CreateCaisseDto } from './dto/create-caisse.dto';
import { UpdateCaisseDto } from './dto/update-caisse.dto';

@ApiTags('caisses')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('caisses')
export class CaissesController {
  constructor(private readonly caissesService: CaissesService) {}

  @Post()
  @Roles(UserRole.ADMIN, UserRole.TRESORIER)
  create(@Body() dto: CreateCaisseDto, @Req() req: any) {
    return this.caissesService.create(dto, req.user._id);
  }

  @Get()
  @Roles(UserRole.ADMIN, UserRole.TRESORIER, UserRole.MEMBRE)
  findAll() {
    return this.caissesService.findAll();
  }

  @Get(':id')
  @Roles(UserRole.ADMIN, UserRole.TRESORIER, UserRole.MEMBRE)
  findOne(@Param('id') id: string) {
    return this.caissesService.findOne(id);
  }

  @Patch(':id')
  @Roles(UserRole.ADMIN, UserRole.TRESORIER)
  update(@Param('id') id: string, @Body() dto: UpdateCaisseDto) {
    return this.caissesService.update(id, dto);
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN)
  remove(@Param('id') id: string) {
    return this.caissesService.remove(id);
  }

  @Post(':id/emarger')
  @Roles(UserRole.TRESORIER)
  emarger(@Param('id') id: string) {
    return this.caissesService.emargerCaisse(id);
  }
}