/* import { Controller, Get, Post, Body, Param } from '@nestjs/common';
import { TontineService } from './tontine.service';
import { CreateTontineDto } from './dto/create-tontine.dto';
import { Tontine } from './tontine.schema';

@Controller('tontine')
export class TontineController {
  constructor(private readonly tontineService: TontineService) {}

  @Post()
  async create(@Body() createTontineDto: CreateTontineDto): Promise<Tontine> {
    return this.tontineService.create(createTontineDto);
  }

  @Get()
  async findAll(): Promise<Tontine[]> {
    return this.tontineService.findAll();
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<Tontine> {
    return this.tontineService.findOne(id);
  }
} */