"use strict";
/* import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type TontineDocument = Tontine & Document;

@Schema()
export class Tontine {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true })
  createdAt: Date;

  @Prop({ required: true })
  updatedAt: Date;

  @Prop({ required: true })
  members: string[]; // Array of user IDs or usernames
}

export const TontineSchema = SchemaFactory.createForClass(Tontine); */ 
