// Script de test pour vérifier le flux d'authentification complet
const http = require('http');

const API_BASE = 'http://localhost:3000';

// Données de test
const testUser = {
  username: 'testuser',
  password: 'test123',
  nom: '<PERSON><PERSON>',
  prenom: '<PERSON>',
  email: '<EMAIL>',
  telephone: '+237123456789',
  role: 'membre',
  statut: 'actif'
};

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsed
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', (e) => {
      reject(e);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testAuthFlow() {
  console.log('🧪 Test du flux d\'authentification complet\n');

  try {
    // 1. Test d'inscription
    console.log('1️⃣ Test d\'inscription...');
    const registerOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/auth/register',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const registerResponse = await makeRequest(registerOptions, testUser);
    console.log(`   Status: ${registerResponse.statusCode}`);
    
    if (registerResponse.statusCode === 201) {
      console.log('   ✅ Inscription réussie');
      console.log(`   📝 Utilisateur créé: ${testUser.username}`);
    } else if (registerResponse.statusCode === 409) {
      console.log('   ⚠️  Utilisateur existe déjà (normal si déjà testé)');
    } else {
      console.log('   ❌ Erreur d\'inscription:', registerResponse.data);
    }

    // 2. Test de connexion
    console.log('\n2️⃣ Test de connexion...');
    const loginOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const loginData = {
      username: testUser.username,
      password: testUser.password
    };

    const loginResponse = await makeRequest(loginOptions, loginData);
    console.log(`   Status: ${loginResponse.statusCode}`);
    
    if (loginResponse.statusCode === 200) {
      console.log('   ✅ Connexion réussie');
      console.log(`   🔑 Token reçu: ${loginResponse.data.access_token ? 'Oui' : 'Non'}`);
      console.log(`   👤 Utilisateur: ${loginResponse.data.user?.prenom} ${loginResponse.data.user?.nom}`);
      console.log(`   🎭 Rôle: ${loginResponse.data.user?.role}`);
      
      // 3. Test d'accès à une route protégée
      console.log('\n3️⃣ Test d\'accès aux données utilisateur...');
      const usersOptions = {
        hostname: 'localhost',
        port: 3000,
        path: '/users',
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${loginResponse.data.access_token}`,
          'Content-Type': 'application/json',
        }
      };

      const usersResponse = await makeRequest(usersOptions);
      console.log(`   Status: ${usersResponse.statusCode}`);
      
      if (usersResponse.statusCode === 200) {
        console.log('   ✅ Accès aux données autorisé');
        console.log(`   📊 Nombre d'utilisateurs: ${Array.isArray(usersResponse.data) ? usersResponse.data.length : 'N/A'}`);
      } else {
        console.log('   ❌ Accès refusé:', usersResponse.data);
      }
      
    } else {
      console.log('   ❌ Erreur de connexion:', loginResponse.data);
    }

    // 4. Test de connexion avec mauvais credentials
    console.log('\n4️⃣ Test avec mauvais credentials...');
    const badLoginData = {
      username: testUser.username,
      password: 'wrongpassword'
    };

    const badLoginResponse = await makeRequest(loginOptions, badLoginData);
    console.log(`   Status: ${badLoginResponse.statusCode}`);
    
    if (badLoginResponse.statusCode === 401) {
      console.log('   ✅ Rejet correct des mauvais credentials');
    } else {
      console.log('   ❌ Comportement inattendu:', badLoginResponse.data);
    }

    console.log('\n🎉 Tests terminés !');
    
  } catch (error) {
    console.error('❌ Erreur lors des tests:', error.message);
    console.log('\n💡 Assurez-vous que:');
    console.log('   - Le backend est démarré (npm start dans /backend)');
    console.log('   - MongoDB est en cours d\'exécution');
    console.log('   - Le port 3000 est disponible');
  }
}

// Attendre un peu puis lancer les tests
setTimeout(testAuthFlow, 1000);
