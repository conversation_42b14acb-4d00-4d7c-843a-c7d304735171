"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCaisseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const caisse_schema_1 = require("../schemas/caisse.schema");
class CreateCaisseDto {
}
exports.CreateCaisseDto = CreateCaisseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Caisse Principale' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateCaisseDto.prototype, "nom", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: caisse_schema_1.CaisseType }),
    (0, class_validator_1.IsEnum)(caisse_schema_1.CaisseType),
    __metadata("design:type", String)
], CreateCaisseDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 0, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], CreateCaisseDto.prototype, "soldeActuel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, description: 'Obligatoire si type = REUNION' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsMongoId)(),
    __metadata("design:type", String)
], CreateCaisseDto.prototype, "sessionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, description: 'Caisse principale liée, pour type REUNION' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsMongoId)(),
    __metadata("design:type", String)
], CreateCaisseDto.prototype, "caissePrincipaleId", void 0);
