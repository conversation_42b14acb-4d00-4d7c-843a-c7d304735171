{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,gPAAO,EAAC,IAAA,0MAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\n\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\n        destructive:\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\n        outline:\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\n        secondary:\n          'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\n        ghost:\n          'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\n        link: 'text-primary underline-offset-4 hover:underline'\n      },\n      size: {\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\n        icon: 'size-9'\n      }\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default'\n    }\n  }\n);\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'button'> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? Slot : 'button';\n\n  return (\n    <Comp\n      data-slot='button'\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,IAAA,8PAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,yTAAI,GAAG;IAE9B,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nfunction Input({ className, type, ...props }: React.ComponentProps<'input'>) {\n  return (\n    <input\n      type={type}\n      data-slot='input'\n      className={cn(\n        'file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\n        'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]',\n        'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nexport { Input };\n"], "names": [], "mappings": ";;;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,yXAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/layout/header.tsx"], "sourcesContent": ["'use client';\n\nimport { useSession } from 'next-auth/react';\nimport { Bell, Search } from 'lucide-react';\n\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\n\nexport function Header() {\n  const { data: session } = useSession();\n\n  return (\n    <header className=\"bg-white border-b border-gray-200 px-6 py-4\">\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">Dashboard</h2>\n        </div>\n\n        <div className=\"flex items-center space-x-4\">\n          {/* Search */}\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n            <Input\n              placeholder=\"Search...\"\n              className=\"pl-10 w-64\"\n            />\n          </div>\n\n          {/* Notifications */}\n          <Button variant=\"ghost\" size=\"icon\">\n            <Bell className=\"h-5 w-5\" />\n          </Button>\n\n          {/* User info */}\n          {session?.user && (\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center\">\n                <span className=\"text-white text-sm font-medium\">\n                  {session.user.name?.charAt(0) || 'U'}\n                </span>\n              </div>\n              <span className=\"text-sm font-medium text-gray-700\">\n                {session.user.name}\n              </span>\n            </div>\n          )}\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAEA;AACA;AANA;;;;;;AAQO,SAAS;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,2QAAU;IAEpC,qBACE,yXAAC;QAAO,WAAU;kBAChB,cAAA,yXAAC;YAAI,WAAU;;8BACb,yXAAC;oBAAI,WAAU;8BACb,cAAA,yXAAC;wBAAG,WAAU;kCAAsC;;;;;;;;;;;8BAGtD,yXAAC;oBAAI,WAAU;;sCAEb,yXAAC;4BAAI,WAAU;;8CACb,yXAAC,gTAAM;oCAAC,WAAU;;;;;;8CAClB,yXAAC,sJAAK;oCACJ,aAAY;oCACZ,WAAU;;;;;;;;;;;;sCAKd,yXAAC,wJAAM;4BAAC,SAAQ;4BAAQ,MAAK;sCAC3B,cAAA,yXAAC,0SAAI;gCAAC,WAAU;;;;;;;;;;;wBAIjB,SAAS,sBACR,yXAAC;4BAAI,WAAU;;8CACb,yXAAC;oCAAI,WAAU;8CACb,cAAA,yXAAC;wCAAK,WAAU;kDACb,QAAQ,IAAI,CAAC,IAAI,EAAE,OAAO,MAAM;;;;;;;;;;;8CAGrC,yXAAC;oCAAK,WAAU;8CACb,QAAQ,IAAI,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlC", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/layout/sidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport { usePathname } from \"next/navigation\";\nimport { signOut, useSession } from \"next-auth/react\";\nimport {\n\tHome,\n\tUsers,\n\tSettings,\n\tLogOut,\n\tMenu,\n\tX,\n\tChevronLeft,\n\tChevronRight,\n\tCalendar,\n\tWallet,\n\tBarChart3,\n} from \"lucide-react\";\n\nimport { Button } from \"@/components/ui/button\";\nimport { cn } from \"@/lib/utils\";\n\nconst navigation = [\n\t{ name: \"Dashboard\", href: \"/dashboard\", icon: Home },\n\t{\n\t\tname: \"Membre<PERSON>\",\n\t\thref: \"/dashboard/members\",\n\t\ticon: Users,\n\t\troles: [\"admin\", \"tresorier\"],\n\t},\n\t{\n\t\tname: \"Sessions\",\n\t\thref: \"/dashboard/sessions\",\n\t\ticon: Calendar,\n\t\troles: [\"admin\", \"tresorier\", \"membre\"],\n\t},\n\t{\n\t\tname: \"<PERSON>ais<PERSON>\",\n\t\thref: \"/dashboard/caisses\",\n\t\ticon: Wallet,\n\t\troles: [\"admin\", \"tresorier\"],\n\t},\n\t{\n\t\tname: \"Rapports\",\n\t\thref: \"/dashboard/reports\",\n\t\ticon: BarChart3,\n\t\troles: [\"admin\", \"tresorier\"],\n\t},\n\t{\n\t\tname: \"<PERSON><PERSON><PERSON><PERSON>\",\n\t\thref: \"/dashboard/settings\",\n\t\ticon: Settings,\n\t\troles: [\"admin\"],\n\t},\n];\n\ninterface SidebarProps {\n\tclassName?: string;\n}\n\nexport function Sidebar({ className }: SidebarProps) {\n\tconst [collapsed, setCollapsed] = useState(false);\n\tconst pathname = usePathname();\n\tconst { data: session } = useSession();\n\n\tconst handleSignOut = () => {\n\t\tsignOut({ callbackUrl: \"/auth/signin\" });\n\t};\n\n\treturn (\n\t\t<div\n\t\t\tclassName={cn(\n\t\t\t\t\"flex flex-col bg-white border-r border-gray-200 transition-all duration-300\",\n\t\t\t\tcollapsed ? \"w-16\" : \"w-64\",\n\t\t\t\tclassName,\n\t\t\t)}\n\t\t>\n\t\t\t{/* Header */}\n\t\t\t<div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n\t\t\t\t{!collapsed && (\n\t\t\t\t\t<h1 className=\"text-xl font-semibold text-gray-900\">Tontine</h1>\n\t\t\t\t)}\n\t\t\t\t<Button\n\t\t\t\t\tvariant=\"ghost\"\n\t\t\t\t\tsize=\"icon\"\n\t\t\t\t\tonClick={() => setCollapsed(!collapsed)}\n\t\t\t\t\tclassName=\"h-8 w-8\"\n\t\t\t\t>\n\t\t\t\t\t{collapsed ? (\n\t\t\t\t\t\t<ChevronRight className=\"h-4 w-4\" />\n\t\t\t\t\t) : (\n\t\t\t\t\t\t<ChevronLeft className=\"h-4 w-4\" />\n\t\t\t\t\t)}\n\t\t\t\t</Button>\n\t\t\t</div>\n\n\t\t\t{/* Navigation */}\n\t\t\t<nav className={cn(\"flex-1 p-4 space-y-2\", collapsed && \"p-2\")}>\n\t\t\t\t{navigation\n\t\t\t\t\t.filter((item) => {\n\t\t\t\t\t\t// Afficher tous les éléments sans rôles spécifiés\n\t\t\t\t\t\tif (!(item as any).roles) return true;\n\n\t\t\t\t\t\t// Vérifier si l'utilisateur a le bon rôle\n\t\t\t\t\t\tconst userRole = (session?.user as any)?.role;\n\t\t\t\t\t\treturn userRole && (item as any).roles.includes(userRole);\n\t\t\t\t\t})\n\t\t\t\t\t.map((item) => {\n\t\t\t\t\t\tconst Icon = item.icon;\n\t\t\t\t\t\tconst isActive =\n\t\t\t\t\t\t\tpathname === item.href || pathname.startsWith(item.href + \"/\");\n\n\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\t<Link\n\t\t\t\t\t\t\t\tkey={item.name}\n\t\t\t\t\t\t\t\thref={item.href}\n\t\t\t\t\t\t\t\tclassName={cn(\n\t\t\t\t\t\t\t\t\t\"flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors\",\n\t\t\t\t\t\t\t\t\tisActive\n\t\t\t\t\t\t\t\t\t\t? \"bg-blue-100 text-blue-700\"\n\t\t\t\t\t\t\t\t\t\t: \"text-gray-600 hover:bg-gray-100 hover:text-gray-900\",\n\t\t\t\t\t\t\t\t\tcollapsed && \"justify-center px-1 py-2\",\n\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<Icon className={cn(\"h-5 w-5\", !collapsed && \"mr-3\")} />\n\t\t\t\t\t\t\t\t{!collapsed && item.name}\n\t\t\t\t\t\t\t</Link>\n\t\t\t\t\t\t);\n\t\t\t\t\t})}\n\t\t\t</nav>\n\n\t\t\t{/* User section */}\n\t\t\t<div className=\"p-4 border-t border-gray-200\">\n\t\t\t\t{session?.user && (\n\t\t\t\t\t<div className={cn(\"mb-3\", collapsed && \"text-center\")}>\n\t\t\t\t\t\t{!collapsed && (\n\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t<p className=\"text-sm font-medium text-gray-900\">\n\t\t\t\t\t\t\t\t\t{session.user.name}\n\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t<p className=\"text-xs text-gray-500\">{session.user.email}</p>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t)}\n\t\t\t\t\t</div>\n\t\t\t\t)}\n\t\t\t\t<Button\n\t\t\t\t\tvariant=\"ghost\"\n\t\t\t\t\tonClick={handleSignOut}\n\t\t\t\t\tclassName={cn(\n\t\t\t\t\t\t\"w-full justify-start text-gray-600 hover:text-gray-900\",\n\t\t\t\t\t\tcollapsed && \"justify-center px-0\",\n\t\t\t\t\t)}\n\t\t\t\t>\n\t\t\t\t\t<LogOut className={cn(\"h-4 w-4\", !collapsed && \"mr-2\")} />\n\t\t\t\t\t{!collapsed && \"Sign out\"}\n\t\t\t\t</Button>\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AArBA;;;;;;;;;AAuBA,MAAM,aAAa;IAClB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,2SAAI;IAAC;IACpD;QACC,MAAM;QACN,MAAM;QACN,MAAM,6SAAK;QACX,OAAO;YAAC;YAAS;SAAY;IAC9B;IACA;QACC,MAAM;QACN,MAAM;QACN,MAAM,sTAAQ;QACd,OAAO;YAAC;YAAS;YAAa;SAAS;IACxC;IACA;QACC,MAAM;QACN,MAAM;QACN,MAAM,gTAAM;QACZ,OAAO;YAAC;YAAS;SAAY;IAC9B;IACA;QACC,MAAM;QACN,MAAM;QACN,MAAM,+TAAS;QACf,OAAO;YAAC;YAAS;SAAY;IAC9B;IACA;QACC,MAAM;QACN,MAAM;QACN,MAAM,sTAAQ;QACd,OAAO;YAAC;SAAQ;IACjB;CACA;AAMM,SAAS,QAAQ,EAAE,SAAS,EAAgB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,4VAAQ,EAAC;IAC3C,MAAM,WAAW,IAAA,4RAAW;IAC5B,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,2QAAU;IAEpC,MAAM,gBAAgB;QACrB,IAAA,wQAAO,EAAC;YAAE,aAAa;QAAe;IACvC;IAEA,qBACC,yXAAC;QACA,WAAW,IAAA,qIAAE,EACZ,+EACA,YAAY,SAAS,QACrB;;0BAID,yXAAC;gBAAI,WAAU;;oBACb,CAAC,2BACD,yXAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAErD,yXAAC,wJAAM;wBACN,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,aAAa,CAAC;wBAC7B,WAAU;kCAET,0BACA,yXAAC,sUAAY;4BAAC,WAAU;;;;;iDAExB,yXAAC,mUAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAM1B,yXAAC;gBAAI,WAAW,IAAA,qIAAE,EAAC,wBAAwB,aAAa;0BACtD,WACC,MAAM,CAAC,CAAC;oBACR,kDAAkD;oBAClD,IAAI,CAAC,AAAC,KAAa,KAAK,EAAE,OAAO;oBAEjC,0CAA0C;oBAC1C,MAAM,WAAY,SAAS,MAAc;oBACzC,OAAO,YAAY,AAAC,KAAa,KAAK,CAAC,QAAQ,CAAC;gBACjD,GACC,GAAG,CAAC,CAAC;oBACL,MAAM,OAAO,KAAK,IAAI;oBACtB,MAAM,WACL,aAAa,KAAK,IAAI,IAAI,SAAS,UAAU,CAAC,KAAK,IAAI,GAAG;oBAE3D,qBACC,yXAAC,kTAAI;wBAEJ,MAAM,KAAK,IAAI;wBACf,WAAW,IAAA,qIAAE,EACZ,gFACA,WACG,8BACA,uDACH,aAAa;;0CAGd,yXAAC;gCAAK,WAAW,IAAA,qIAAE,EAAC,WAAW,CAAC,aAAa;;;;;;4BAC5C,CAAC,aAAa,KAAK,IAAI;;uBAXnB,KAAK,IAAI;;;;;gBAcjB;;;;;;0BAIF,yXAAC;gBAAI,WAAU;;oBACb,SAAS,sBACT,yXAAC;wBAAI,WAAW,IAAA,qIAAE,EAAC,QAAQ,aAAa;kCACtC,CAAC,2BACD,yXAAC;;8CACA,yXAAC;oCAAE,WAAU;8CACX,QAAQ,IAAI,CAAC,IAAI;;;;;;8CAEnB,yXAAC;oCAAE,WAAU;8CAAyB,QAAQ,IAAI,CAAC,KAAK;;;;;;;;;;;;;;;;;kCAK5D,yXAAC,wJAAM;wBACN,SAAQ;wBACR,SAAS;wBACT,WAAW,IAAA,qIAAE,EACZ,0DACA,aAAa;;0CAGd,yXAAC,oTAAM;gCAAC,WAAW,IAAA,qIAAE,EAAC,WAAW,CAAC,aAAa;;;;;;4BAC9C,CAAC,aAAa;;;;;;;;;;;;;;;;;;;AAKpB", "debugId": null}}, {"offset": {"line": 491, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/app/dashboard/layout.tsx"], "sourcesContent": ["\"use client\";\nimport { <PERSON><PERSON> } from \"@/components/layout/header\";\nimport { Sidebar } from \"@/components/layout/sidebar\";\n\nexport default function DashboardLayout({\n\tchildren,\n}: {\n\tchildren: React.ReactNode;\n}) {\n\treturn (\n\t\t<div className=\"min-h-screen bg-gray-50 flex\">\n\t\t\t<Sidebar />\n\t\t\t<div className=\"flex-1 flex flex-col\">\n\t\t\t\t<Header />\n\t\t\t\t<main className=\"flex-1 p-6\">{children}</main>\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAFA;;;;AAIe,SAAS,gBAAgB,EACvC,QAAQ,EAGR;IACA,qBACC,yXAAC;QAAI,WAAU;;0BACd,yXAAC,8JAAO;;;;;0BACR,yXAAC;gBAAI,WAAU;;kCACd,yXAAC,4JAAM;;;;;kCACP,yXAAC;wBAAK,WAAU;kCAAc;;;;;;;;;;;;;;;;;;AAIlC", "debugId": null}}]}