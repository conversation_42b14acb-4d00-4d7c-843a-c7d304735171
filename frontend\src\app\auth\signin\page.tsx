"use client";

import { useState } from "react";
import { signIn } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import Link from "next/link";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";

const loginSchema = z.object({
	username: z.string().min(1, "Username is required"),
	password: z.string().min(4, "Password is required"),
});

type LoginForm = z.infer<typeof loginSchema>;

export default function SignInPage() {
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const router = useRouter();

	const form = useForm<LoginForm>({
		resolver: zodResolver(loginSchema),
		defaultValues: {
			username: "",
			password: "",
		},
	});

	const onSubmit = async (data: LoginForm) => {
		setIsLoading(true);
		setError(null);

		try {
			const result = await signIn("credentials", {
				username: data.username,
				password: data.password,
				redirect: false,
			});

			if (result?.error) {
				setError("Nom d'utilisateur ou mot de passe incorrect");
			} else {
				router.push("/dashboard");
			}
		} catch (error) {
			console.error("Erreur de connexion:", error);
			setError("Erreur de connexion au serveur. Veuillez réessayer.");
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
			<Card className="w-full max-w-md">
				<CardHeader className="space-y-1">
					<CardTitle className="text-2xl font-bold text-center">
						Connexion
					</CardTitle>
					<CardDescription className="text-center">
						Connectez-vous à votre compte Tontine
					</CardDescription>
				</CardHeader>
				<CardContent>
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
							<FormField
								control={form.control}
								name="username"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Nom d'utilisateur</FormLabel>
										<FormControl>
											<Input
												placeholder="Entrez votre nom d'utilisateur"
												{...field}
												disabled={isLoading}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="password"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Mot de passe</FormLabel>
										<FormControl>
											<Input
												type="password"
												placeholder="Entrez votre mot de passe"
												{...field}
												disabled={isLoading}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							{error && (
								<div className="text-red-600 text-sm text-center">{error}</div>
							)}
							<Button type="submit" className="w-full" disabled={isLoading}>
								{isLoading ? "Connexion..." : "Se connecter"}
							</Button>
						</form>
					</Form>

					<div className="mt-4 text-center text-sm text-gray-600">
						Pas encore de compte ?{" "}
						<Link
							href="/auth/register"
							className="text-blue-600 hover:underline"
						>
							Créer un compte
						</Link>
						<div className="text-xs text-orange-600 mt-1">
							(Temporaire - pour les tests)
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
