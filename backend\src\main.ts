import "reflect-metadata";
import { ValidationPipe } from "@nestjs/common";
import { NestFactory } from "@nestjs/core";
import { AppModule } from "./app.module";
import { DocumentBuilder, SwaggerModule } from "@nestjs/swagger";

async function bootstrap() {
	const app = await NestFactory.create(AppModule);

	// Active la validation globale des DTOs
	app.useGlobalPipes(
		new ValidationPipe({
			whitelist: true, // Ignore les propriétés non définies dans le DTO
			forbidNonWhitelisted: true, // Erreur si des propriétés non attendues sont envoyées
			forbidUnknownValues: true, // Erreur si le body est vide ou non conforme
			transform: true, // Transforme le payload en instance du DTO
		}),
	);

	const config = new DocumentBuilder()
		.setTitle("Tontine API")
		.setDescription("API de gestion de tontine")
		.setVersion("1.0")
		.addBearerAuth()
		.build();

	app.enableCors({
		origin: ["http://localhost:3000", "https://myapp.com"],
		methods: "GET,HEAD,PUT,PATCH,POST,DELETE",
		credentials: true,
	});
	const document = SwaggerModule.createDocument(app, config);
	SwaggerModule.setup("api", app, document);

	const port = process.env.PORT || 4000;
	await app.listen(port);
	console.log(`Application is running on: http://localhost:${port}`);
}

bootstrap();
