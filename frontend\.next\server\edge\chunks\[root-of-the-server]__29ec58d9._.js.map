{"version": 3, "sources": [], "sections": [{"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/src/lib/api.ts"], "sourcesContent": ["import { signOut } from \"next-auth/react\";\nimport { signOut as ServerSignOut } from \"./auth\";\n\n// Configuration de l'API\nexport const API_BASE_URL =\n\tprocess.env.NEXT_PUBLIC_API_URL || \"http://localhost:4000\";\n\n// Types pour l'authentification\nexport interface LoginRequest {\n\tusername: string;\n\tpassword: string;\n}\n\nexport interface LoginResponse {\n\taccess_token: string;\n\tuser: {\n\t\tid: string;\n\t\tusername: string;\n\t\tnom: string;\n\t\tprenom: string;\n\t\temail: string;\n\t\trole: \"admin\" | \"tresorier\" | \"membre\";\n\t\tstatut: \"actif\" | \"en_attente\" | \"suspendu\";\n\t};\n}\n\nexport interface RegisterRequest {\n\tusername: string;\n\tpassword: string;\n\tnom: string;\n\tprenom: string;\n\temail: string;\n\ttelephone: string;\n\trole: \"admin\" | \"tresorier\" | \"membre\";\n\tstatut?: \"actif\" | \"en_attente\" | \"suspendu\";\n}\n\nexport interface ApiError {\n\tmessage: string;\n\tstatusCode: number;\n\terror?: string;\n}\n\n// Service API\nexport class ApiService {\n\tprivate baseUrl: string;\n\n\tconstructor(baseUrl: string = API_BASE_URL) {\n\t\tthis.baseUrl = baseUrl;\n\t}\n\n\tprivate async request<T>(\n\t\tendpoint: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> {\n\t\tconst url = `${this.baseUrl}${endpoint}`;\n\n\t\tconst config: RequestInit = {\n\t\t\theaders: {\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\t...options.headers,\n\t\t\t},\n\t\t\t...options,\n\t\t};\n\n\t\ttry {\n\t\t\tconst response = await fetch(url, config);\n\n\t\t\tif (!response.ok) {\n\t\t\t\tconst errorData: ApiError = await response.json().catch(() => ({\n\t\t\t\t\tmessage: \"Une erreur est survenue\",\n\t\t\t\t\tstatusCode: response.status,\n\t\t\t\t}));\n\n\t\t\t\t// 🚨 Gestion du cas 401\n\t\t\t\tif (response.status === 401) {\n\t\t\t\t\t// Si côté client → on déconnecte\n\t\t\t\t\tif (typeof window !== \"undefined\") {\n\t\t\t\t\t\tawait signOut({ callbackUrl: \"/auth/signin\" });\n\t\t\t\t\t} else {\n\t\t\t\t\t\tawait ServerSignOut({ redirectTo: \"/auth/signin\" });\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthrow new Error(errorData.message || `HTTP ${response.status}`);\n\t\t\t}\n\n\t\t\treturn await response.json();\n\t\t} catch (error) {\n\t\t\tconsole.log(error);\n\n\t\t\tif (error instanceof Error) {\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t\tthrow new Error(\"Erreur de connexion au serveur\");\n\t\t}\n\t}\n\n\t// Authentification\n\tasync login(credentials: LoginRequest): Promise<LoginResponse> {\n\t\treturn this.request<LoginResponse>(\"/auth/login\", {\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(credentials),\n\t\t});\n\t}\n\n\tasync register(\n\t\tuserData: RegisterRequest,\n\t): Promise<{ message: string; user: any }> {\n\t\treturn this.request<{ message: string; user: any }>(\"/auth/register\", {\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(userData),\n\t\t});\n\t}\n\n\t// Méthodes avec authentification\n\tasync authenticatedRequest<T>(\n\t\tendpoint: string,\n\t\ttoken: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> {\n\t\treturn this.request<T>(endpoint, {\n\t\t\t...options,\n\t\t\theaders: {\n\t\t\t\t...options.headers,\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\tAuthorization: `Bearer ${token}`,\n\t\t\t},\n\t\t});\n\t}\n\n\t// Utilisateurs\n\tasync getUsers(token: string): Promise<any[]> {\n\t\treturn this.authenticatedRequest<any[]>(\"/users\", token);\n\t}\n\n\tasync getUser(id: string, token: string): Promise<any> {\n\t\treturn this.authenticatedRequest<any>(`/users/${id}`, token);\n\t}\n}\n\n// Instance par défaut\nexport const apiService = new ApiService();\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAGO,MAAM,eACZ,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AAuC7B,MAAM;IACJ,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC3C,IAAI,CAAC,OAAO,GAAG;IAChB;IAEA,MAAc,QACb,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACZ;QACb,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QAExC,MAAM,SAAsB;YAC3B,SAAS;gBACR,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACnB;YACA,GAAG,OAAO;QACX;QAEA,IAAI;YACH,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBACjB,MAAM,YAAsB,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;wBAC9D,SAAS;wBACT,YAAY,SAAS,MAAM;oBAC5B,CAAC;gBAED,wBAAwB;gBACxB,IAAI,SAAS,MAAM,KAAK,KAAK;oBAC5B,iCAAiC;oBACjC;;yBAEO;wBACN,MAAM,IAAA,iJAAa,EAAC;4BAAE,YAAY;wBAAe;oBAClD;gBACD;gBACA,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;YAC/D;YAEA,OAAO,MAAM,SAAS,IAAI;QAC3B,EAAE,OAAO,OAAO;YACf,QAAQ,GAAG,CAAC;YAEZ,IAAI,iBAAiB,OAAO;gBAC3B,MAAM;YACP;YACA,MAAM,IAAI,MAAM;QACjB;IACD;IAEA,mBAAmB;IACnB,MAAM,MAAM,WAAyB,EAA0B;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAgB,eAAe;YACjD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACtB;IACD;IAEA,MAAM,SACL,QAAyB,EACiB;QAC1C,OAAO,IAAI,CAAC,OAAO,CAAiC,kBAAkB;YACrE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACtB;IACD;IAEA,iCAAiC;IACjC,MAAM,qBACL,QAAgB,EAChB,KAAa,EACb,UAAuB,CAAC,CAAC,EACZ;QACb,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAChC,GAAG,OAAO;YACV,SAAS;gBACR,GAAG,QAAQ,OAAO;gBAClB,gBAAgB;gBAChB,eAAe,CAAC,OAAO,EAAE,OAAO;YACjC;QACD;IACD;IAEA,eAAe;IACf,MAAM,SAAS,KAAa,EAAkB;QAC7C,OAAO,IAAI,CAAC,oBAAoB,CAAQ,UAAU;IACnD;IAEA,MAAM,QAAQ,EAAU,EAAE,KAAa,EAAgB;QACtD,OAAO,IAAI,CAAC,oBAAoB,CAAM,CAAC,OAAO,EAAE,IAAI,EAAE;IACvD;AACD;AAGO,MAAM,aAAa,IAAI"}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/src/lib/auth.ts"], "sourcesContent": ["import NextAuth from \"next-auth\";\nimport C<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from \"next-auth/providers/credentials\";\nimport { z } from \"zod\";\nimport { apiService } from \"./api\";\n\nconst loginSchema = z.object({\n\tusername: z.string().min(1, \"Username is required\"),\n\tpassword: z.string().min(1, \"Password is required\"),\n});\n\nexport const { handlers, signIn, signOut, auth } = NextAuth({\n\tproviders: [\n\t\tCredentialsProvider({\n\t\t\tname: \"credentials\",\n\t\t\tcredentials: {\n\t\t\t\tusername: { label: \"Username\", type: \"text\" },\n\t\t\t\tpassword: { label: \"Password\", type: \"password\" },\n\t\t\t},\n\t\t\tasync authorize(credentials) {\n\t\t\t\ttry {\n\t\t\t\t\tconst { username, password } = loginSchema.parse(credentials);\n\n\t\t\t\t\t// Authentification avec l'API backend\n\t\t\t\t\tconst response = await apiService.login({ username, password });\n\n\t\t\t\t\tif (response.access_token && response.user) {\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\tid: response.user.id,\n\t\t\t\t\t\t\tname: `${response.user.prenom} ${response.user.nom}`,\n\t\t\t\t\t\t\temail: response.user.email,\n\t\t\t\t\t\t\tusername: response.user.username,\n\t\t\t\t\t\t\trole: response.user.role,\n\t\t\t\t\t\t\tstatut: response.user.statut,\n\t\t\t\t\t\t\taccessToken: response.access_token,\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\n\t\t\t\t\treturn null;\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error(\"Erreur d'authentification:\", error);\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t},\n\t\t}),\n\t],\n\tpages: {\n\t\tsignIn: \"/auth/signin\",\n\t},\n\tcallbacks: {\n\t\tauthorized: ({ auth }) => !!auth,\n\t\tasync jwt({ token, user }) {\n\t\t\tif (user) {\n\t\t\t\ttoken.username = user.username;\n\t\t\t\ttoken.role = user.role;\n\t\t\t\ttoken.statut = user.statut;\n\t\t\t\ttoken.accessToken = user.accessToken;\n\t\t\t}\n\t\t\treturn token;\n\t\t},\n\t\tasync session({ session, token }) {\n\t\t\tif (token) {\n\t\t\t\tsession.user.id = token.sub || \"\";\n\t\t\t\tsession.user.username = token.username as string;\n\t\t\t\tsession.user.role = token.role as \"admin\" | \"tresorier\" | \"membre\";\n\t\t\t\tsession.user.statut = token.statut as\n\t\t\t\t\t| \"actif\"\n\t\t\t\t\t| \"en_attente\"\n\t\t\t\t\t| \"suspendu\";\n\t\t\t\tsession.accessToken = token.accessToken as string;\n\t\t\t}\n\t\t\treturn session;\n\t\t},\n\t},\n\tsession: {\n\t\tstrategy: \"jwt\",\n\t},\n});\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AAAA;AACA;AACA;;;;;AAEA,MAAM,cAAc,sPAAC,CAAC,MAAM,CAAC;IAC5B,UAAU,sPAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,UAAU,sPAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC7B;AAEO,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,IAAA,gSAAQ,EAAC;IAC3D,WAAW;QACV,IAAA,qPAAmB,EAAC;YACnB,MAAM;YACN,aAAa;gBACZ,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAO;gBAC5C,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YACjD;YACA,MAAM,WAAU,WAAW;gBAC1B,IAAI;oBACH,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,YAAY,KAAK,CAAC;oBAEjD,sCAAsC;oBACtC,MAAM,WAAW,MAAM,mJAAU,CAAC,KAAK,CAAC;wBAAE;wBAAU;oBAAS;oBAE7D,IAAI,SAAS,YAAY,IAAI,SAAS,IAAI,EAAE;wBAC3C,OAAO;4BACN,IAAI,SAAS,IAAI,CAAC,EAAE;4BACpB,MAAM,GAAG,SAAS,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE;4BACpD,OAAO,SAAS,IAAI,CAAC,KAAK;4BAC1B,UAAU,SAAS,IAAI,CAAC,QAAQ;4BAChC,MAAM,SAAS,IAAI,CAAC,IAAI;4BACxB,QAAQ,SAAS,IAAI,CAAC,MAAM;4BAC5B,aAAa,SAAS,YAAY;wBACnC;oBACD;oBAEA,OAAO;gBACR,EAAE,OAAO,OAAO;oBACf,QAAQ,KAAK,CAAC,8BAA8B;oBAC5C,OAAO;gBACR;YACD;QACD;KACA;IACD,OAAO;QACN,QAAQ;IACT;IACA,WAAW;QACV,YAAY,CAAC,EAAE,IAAI,EAAE,GAAK,CAAC,CAAC;QAC5B,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACxB,IAAI,MAAM;gBACT,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,MAAM,GAAG,KAAK,MAAM;gBAC1B,MAAM,WAAW,GAAG,KAAK,WAAW;YACrC;YACA,OAAO;QACR;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC/B,IAAI,OAAO;gBACV,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG,IAAI;gBAC/B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM;gBAIlC,QAAQ,WAAW,GAAG,MAAM,WAAW;YACxC;YACA,OAAO;QACR;IACD;IACA,SAAS;QACR,UAAU;IACX;AACD"}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from \"next/server\";\r\nimport { auth } from \"@/lib/auth\";\r\n\r\nexport default auth((req) => {\r\n\tconst { nextUrl } = req;\r\n\r\n\t// Ex: protéger tout sauf /login et /public\r\n\tconst isAuthPage =\r\n\t\tnextUrl.pathname.startsWith(\"/auth\") || nextUrl.pathname === \"/\";\r\n\r\n\tif (!req.auth && !isAuthPage) {\r\n\t\t// pas connecté -> redirection login\r\n\t\treturn NextResponse.redirect(new URL(\"/auth/signin\", nextUrl));\r\n\t}\r\n\r\n\t// si déjà connecté et tente d'aller sur /login -> on redirige ailleurs\r\n\tif (\r\n\t\t(req.auth && nextUrl.pathname.startsWith(\"/auth/signin\")) ||\r\n\t\tnextUrl.pathname === \"/\"\r\n\t) {\r\n\t\treturn NextResponse.redirect(new URL(\"/dashboard\", nextUrl));\r\n\t}\r\n\r\n\treturn NextResponse.next();\r\n});\r\n\r\n// ⬇️ obligatoire : config matcher\r\nexport const config = {\r\n\tmatcher: [\r\n\t\t\"/((?!api|_next/static|_next/image|favicon.ico).*)\", // toutes les routes sauf API/static\r\n\t],\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;;;uCAEe,IAAA,8IAAI,EAAC,CAAC;IACpB,MAAM,EAAE,OAAO,EAAE,GAAG;IAEpB,2CAA2C;IAC3C,MAAM,aACL,QAAQ,QAAQ,CAAC,UAAU,CAAC,YAAY,QAAQ,QAAQ,KAAK;IAE9D,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY;QAC7B,oCAAoC;QACpC,OAAO,2UAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,gBAAgB;IACtD;IAEA,uEAAuE;IACvE,IACC,AAAC,IAAI,IAAI,IAAI,QAAQ,QAAQ,CAAC,UAAU,CAAC,mBACzC,QAAQ,QAAQ,KAAK,KACpB;QACD,OAAO,2UAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc;IACpD;IAEA,OAAO,2UAAY,CAAC,IAAI;AACzB;AAGO,MAAM,SAAS;IACrB,SAAS;QACR;KACA;AACF"}}]}