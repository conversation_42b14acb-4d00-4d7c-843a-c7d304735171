import NextAuth from "next-auth"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      name?: string | null
      email?: string | null
      image?: string | null
      username: string
      role: 'admin' | 'tresorier' | 'membre'
      statut: 'actif' | 'en_attente' | 'suspendu'
    }
    accessToken: string
  }

  interface User {
    id: string
    name?: string | null
    email?: string | null
    image?: string | null
    username: string
    role: 'admin' | 'tresorier' | 'membre'
    statut: 'actif' | 'en_attente' | 'suspendu'
    accessToken: string
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    username: string
    role: 'admin' | 'tresorier' | 'membre'
    statut: 'actif' | 'en_attente' | 'suspendu'
    accessToken: string
  }
}
