import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { SessionsModule } from './sessions/sessions.module';
import { ReunionsModule } from './reunions/reunions.module';
import { CaissesModule } from './caisses/caisses.module';

@Module({
  imports: [
    MongooseModule.forRoot('mongodb://localhost:27017/tontine-mvp'),
    UsersModule,
    AuthModule,
    SessionsModule,
    ReunionsModule,
    CaissesModule,
  ],
})
export class AppModule {}