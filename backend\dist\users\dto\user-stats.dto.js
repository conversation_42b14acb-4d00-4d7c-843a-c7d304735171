"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserStatsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class UserStatsDto {
}
exports.UserStatsDto = UserStatsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 25, description: "Nombre total d'utilisateurs" }),
    __metadata("design:type", Number)
], UserStatsDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: { admin: 1, tresorier: 2, membre: 22 },
        description: "Répartition par rôle",
    }),
    __metadata("design:type", Object)
], UserStatsDto.prototype, "byRole", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: { actif: 20, en_attente: 3, suspendu: 2 },
        description: "Répartition par statut",
    }),
    __metadata("design:type", Object)
], UserStatsDto.prototype, "byStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: { admin: 1, tresorier: 2, membre: 17 },
        description: "Membres actifs par rôle",
    }),
    __metadata("design:type", Object)
], UserStatsDto.prototype, "activeByRole", void 0);
