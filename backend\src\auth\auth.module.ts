import { Modu<PERSON> } from "@nestjs/common";
import { UsersModule } from "../users/users.module";
import { JwtModule } from "@nestjs/jwt";
import { PassportModule } from "@nestjs/passport";

import { AuthService } from "./auth.service";
import { AuthController } from "./auth.controller";
import { JwtStrategy } from "./jwt.strategy";

@Module({
	imports: [
		UsersModule,
		PassportModule,
		JwtModule.register({
			secret: "temporaire", //process.env.JWT_SECRET
			signOptions: { expiresIn: "8400s" }, // 24h token expiry
		}),
	],
	providers: [AuthService, JwtStrategy],
	controllers: [AuthController],
	exports: [AuthService],
})
export class AuthModule {}
