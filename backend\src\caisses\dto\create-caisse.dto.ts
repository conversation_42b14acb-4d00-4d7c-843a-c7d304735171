import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsMongoId, IsNotEmpty, IsNumber, IsOptional, IsPositive, IsString } from 'class-validator';
import { CaisseType } from '../schemas/caisse.schema';

export class CreateCaisseDto {
  @ApiProperty({ example: 'Caisse Principale' })
  @IsString()
  @IsNotEmpty()
  nom!: string;

  @ApiProperty({ enum: CaisseType })
  @IsEnum(CaisseType)
  type!: CaisseType;

  @ApiProperty({ example: 0, required: false })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  soldeActuel?: number;

  @ApiProperty({ required: false, description: 'Obligatoire si type = REUNION' })
  @IsOptional()
  @IsMongoId()
  sessionId?: string;

/*   @ApiProperty({ required: false, description: 'Lié à la réunion si type REUNION' })
  @IsOptional()
  @IsMongoId()
  reunionId?: string; */

  @ApiProperty({ required: false, description: 'Caisse principale liée, pour type REUNION' })
  @IsOptional()
  @IsMongoId()
  caissePrincipaleId?: string;
}