import { Controller, Get, Patch, Body, Param, Delete, UseGuards, Post, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { User, UserRole, UserStatus } from './schemas/user.schema';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserStatsDto } from './dto/user-stats.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../common/guards/role.guard';
import { Roles } from '../common/decorators/roles.decorator';

@ApiTags('Users')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Créer un nouvel utilisateur' })
  @ApiResponse({ status: 201, description: 'Utilisateur créé avec succès' })
  @ApiResponse({ status: 400, description: 'Données invalides' })
  @ApiResponse({ status: 409, description: 'Nom d\'utilisateur ou email déjà utilisé' })
  async create(@Body() createUserDto: CreateUserDto): Promise<User> {
      console.log(createUserDto);
      
    return this.usersService.create(createUserDto);
  }

  @Get()
  @Roles(UserRole.ADMIN, UserRole.TRESORIER)
  @ApiOperation({ summary: 'Récupérer tous les utilisateurs avec filtres optionnels' })
  @ApiResponse({ status: 200, description: 'Liste des utilisateurs récupérée avec succès' })
  @ApiQuery({ name: 'role', enum: UserRole, required: false, description: 'Filtrer par rôle' })
  @ApiQuery({ name: 'statut', enum: UserStatus, required: false, description: 'Filtrer par statut' })
  @ApiQuery({ name: 'search', type: String, required: false, description: 'Rechercher par nom, prénom ou email' })
  async findAll(
    @Query('role') role?: UserRole,
    @Query('statut') statut?: UserStatus,
    @Query('search') search?: string,
  ): Promise<User[]> {
    return this.usersService.findAll(role, statut, search);
  }

  // Endpoints spécialisés pour la gestion des membres (AVANT les routes avec :id)

  @Get('stats')
  @Roles(UserRole.ADMIN, UserRole.TRESORIER)
  @ApiOperation({ summary: 'Obtenir les statistiques des membres' })
  @ApiResponse({ status: 200, description: 'Statistiques récupérées avec succès', type: UserStatsDto })
  async getStats(): Promise<UserStatsDto> {
    return this.usersService.getStats();
  }

  @Get('by-role/:role')
  @Roles(UserRole.ADMIN, UserRole.TRESORIER)
  @ApiOperation({ summary: 'Récupérer les utilisateurs par rôle' })
  @ApiResponse({ status: 200, description: 'Utilisateurs récupérés avec succès' })
  async findByRole(@Param('role') role: UserRole): Promise<User[]> {
    return this.usersService.findByRole(role);
  }

  @Get('by-statut/:statut')
  @Roles(UserRole.ADMIN, UserRole.TRESORIER)
  @ApiOperation({ summary: 'Récupérer les utilisateurs par statut' })
  @ApiResponse({ status: 200, description: 'Utilisateurs récupérés avec succès' })
  async findByStatut(@Param('statut') statut: UserStatus): Promise<User[]> {
    return this.usersService.findByStatut(statut);
  }

  @Get(':id')
  @Roles(UserRole.ADMIN, UserRole.TRESORIER)
  @ApiOperation({ summary: 'Récupérer un utilisateur par ID' })
  @ApiResponse({ status: 200, description: 'Utilisateur trouvé' })
  @ApiResponse({ status: 404, description: 'Utilisateur non trouvé' })
  async findOne(@Param('id') id: string): Promise<User|null> {
    return this.usersService.findOne(id);
  }

  @Patch(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Mettre à jour un utilisateur' })
  @ApiResponse({ status: 200, description: 'Utilisateur mis à jour avec succès' })
  @ApiResponse({ status: 404, description: 'Utilisateur non trouvé' })
  @ApiResponse({ status: 409, description: 'Nom d\'utilisateur ou email déjà utilisé' })
  async update(@Param('id') id: string, @Body() data: UpdateUserDto): Promise<User | null> {
    return this.usersService.update(id, data);
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Supprimer un utilisateur' })
  @ApiResponse({ status: 200, description: 'Utilisateur supprimé avec succès' })
  @ApiResponse({ status: 404, description: 'Utilisateur non trouvé' })
  async remove(@Param('id') id: string): Promise<void | User|null> {
    return this.usersService.remove(id);
  }

  @Patch(':id/statut/:statut')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Changer le statut d\'un membre' })
  @ApiResponse({ status: 200, description: 'Statut mis à jour avec succès' })
  @ApiResponse({ status: 404, description: 'Utilisateur non trouvé' })
  async updateStatut(
    @Param('id') id: string,
    @Param('statut') statut: UserStatus,
  ): Promise<User | null> {
    return this.usersService.updateStatut(id, statut);
  }
}