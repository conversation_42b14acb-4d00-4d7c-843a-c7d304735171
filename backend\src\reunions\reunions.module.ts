import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ReunionsController } from './reunions.controller';
import { ReunionsService } from './reunions.service';
import { Reunion, ReunionSchema } from './schemas/reunion.schema';

@Module({
  imports: [MongooseModule.forFeature([{ name: Reunion.name, schema: ReunionSchema }])],
  controllers: [ReunionsController],
  providers: [ReunionsService],
  exports: [ReunionsService],
})
export class ReunionsModule {}