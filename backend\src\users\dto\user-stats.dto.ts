import { ApiProperty } from "@nestjs/swagger";

export class UserStatsDto {
	@ApiProperty({ example: 25, description: "Nombre total d'utilisateurs" })
	total!: number;

	@ApiProperty({
		example: { admin: 1, tresorier: 2, membre: 22 },
		description: "Répartition par rôle",
	})
	byRole!: Record<string, number>;

	@ApiProperty({
		example: { actif: 20, en_attente: 3, suspendu: 2 },
		description: "Répartition par statut",
	})
	byStatus!: Record<string, number>;

	@ApiProperty({
		example: { admin: 1, tresorier: 2, membre: 17 },
		description: "Membres actifs par rôle",
	})
	activeByRole!: Record<string, number>;
}
