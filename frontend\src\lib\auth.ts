import NextAuth from "next-auth";
import C<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "next-auth/providers/credentials";
import { z } from "zod";
import { apiService } from "./api";

const loginSchema = z.object({
	username: z.string().min(1, "Username is required"),
	password: z.string().min(1, "Password is required"),
});

export const { handlers, signIn, signOut, auth } = NextAuth({
	providers: [
		CredentialsProvider({
			name: "credentials",
			credentials: {
				username: { label: "Username", type: "text" },
				password: { label: "Password", type: "password" },
			},
			async authorize(credentials) {
				try {
					const { username, password } = loginSchema.parse(credentials);

					// Authentification avec l'API backend
					const response = await apiService.login({ username, password });

					if (response.access_token && response.user) {
						return {
							id: response.user.id,
							name: `${response.user.prenom} ${response.user.nom}`,
							email: response.user.email,
							username: response.user.username,
							role: response.user.role,
							statut: response.user.statut,
							accessToken: response.access_token,
						};
					}

					return null;
				} catch (error) {
					console.error("Erreur d'authentification:", error);
					return null;
				}
			},
		}),
	],
	pages: {
		signIn: "/auth/signin",
	},
	callbacks: {
		authorized: ({ auth }) => !!auth,
		async jwt({ token, user }) {
			if (user) {
				token.username = user.username;
				token.role = user.role;
				token.statut = user.statut;
				token.accessToken = user.accessToken;
			}
			return token;
		},
		async session({ session, token }) {
			if (token) {
				session.user.id = token.sub || "";
				session.user.username = token.username as string;
				session.user.role = token.role as "admin" | "tresorier" | "membre";
				session.user.statut = token.statut as
					| "actif"
					| "en_attente"
					| "suspendu";
				session.accessToken = token.accessToken as string;
			}
			return session;
		},
	},
	session: {
		strategy: "jwt",
	},
});
