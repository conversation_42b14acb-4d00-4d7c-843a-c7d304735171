"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON> } from "next/navigation";
import { useSession } from "next-auth/react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { useApi } from "@/hooks/use-api";
const memberSchema = z.object({
	username: z
		.string()
		.min(3, "Le nom d'utilisateur doit contenir au moins 3 caractères"),
	password: z
		.string()
		.min(6, "Le mot de passe doit contenir au moins 6 caractères"),
	nom: z.string().min(2, "Le nom doit contenir au moins 2 caractères"),
	prenom: z.string().min(2, "Le prénom doit contenir au moins 2 caractères"),
	email: z.email("Adresse email invalide"),
	telephone: z
		.string()
		.trim()
		.min(8, "Le numéro de téléphone doit contenir au moins 8 caractères"),
	role: z.enum(["admin", "tresorier", "membre"]),
	statut: z.enum(["actif", "en_attente", "suspendu"]),
});

type MemberForm = z.infer<typeof memberSchema>;

export default function NewMemberPage() {
	const { data: session } = useSession();
	const router = useRouter();
	const api = useApi();

	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// Vérifier les permissions
	const canCreateMembers =
		session?.user && (session.user as any).role === "admin";

	const form = useForm<MemberForm>({
		resolver: zodResolver(memberSchema),
		defaultValues: {
			username: "",
			password: "",
			nom: "",
			prenom: "",
			email: "",
			telephone: "",
			role: "membre",
			statut: "actif",
		},
	});

	const onSubmit = async (data: MemberForm) => {
		setIsLoading(true);
		setError(null);
		console.log(data);

		try {
			await api.authenticatedRequest("/users", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(data),
			});

			// Rediriger vers la liste des membres
			router.push("/dashboard/members");
		} catch (error) {
			console.error("Erreur lors de la création du membre:", error);
			if (error instanceof Error) {
				setError(error.message);
			} else {
				setError("Erreur lors de la création du membre. Veuillez réessayer.");
			}
		} finally {
			setIsLoading(false);
		}
	};

	if (!canCreateMembers) {
		return (
			<div className="space-y-6">
				<div className="flex items-center gap-4">
					<Link href="/dashboard/members">
						<Button variant="ghost" size="sm">
							<ArrowLeft className="h-4 w-4 mr-2" />
							Retour
						</Button>
					</Link>
				</div>
				<div className="flex items-center justify-center h-64">
					<div className="text-center">
						<h2 className="text-lg font-semibold text-gray-900">
							Accès refusé
						</h2>
						<p className="text-gray-600">
							Seuls les administrateurs peuvent créer des membres.
						</p>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* En-tête */}
			<div className="flex items-center gap-4">
				<Link href="/dashboard/members">
					<Button variant="ghost" size="sm">
						<ArrowLeft className="h-4 w-4 mr-2" />
						Retour
					</Button>
				</Link>
				<div>
					<h1 className="text-2xl font-bold text-gray-900">Nouveau Membre</h1>
					<p className="text-gray-600">Créer un nouveau membre de la tontine</p>
				</div>
			</div>

			{/* Formulaire */}
			<Card className="max-w-2xl">
				<CardHeader>
					<CardTitle>Informations du membre</CardTitle>
					<CardDescription>
						Remplissez tous les champs pour créer un nouveau membre
					</CardDescription>
				</CardHeader>
				<CardContent>
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
							{/* Informations personnelles */}
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<FormField
									control={form.control}
									name="prenom"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Prénom *</FormLabel>
											<FormControl>
												<Input
													placeholder="Prénom"
													{...field}
													disabled={isLoading}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name="nom"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Nom *</FormLabel>
											<FormControl>
												<Input
													placeholder="Nom de famille"
													{...field}
													disabled={isLoading}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							{/* Informations de contact */}
							<FormField
								control={form.control}
								name="email"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Email *</FormLabel>
										<FormControl>
											<Input
												type="email"
												placeholder="<EMAIL>"
												{...field}
												disabled={isLoading}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="telephone"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Téléphone *</FormLabel>
										<FormControl>
											<Input
												placeholder="+237123456789"
												{...field}
												disabled={isLoading}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Informations de connexion */}
							<div className="border-t pt-6">
								<h3 className="text-lg font-medium mb-4">
									Informations de connexion
								</h3>

								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<FormField
										control={form.control}
										name="username"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Nom d'utilisateur *</FormLabel>
												<FormControl>
													<Input
														placeholder="nom_utilisateur"
														{...field}
														disabled={isLoading}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="password"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Mot de passe *</FormLabel>
												<FormControl>
													<Input
														type="password"
														placeholder="Minimum 6 caractères"
														{...field}
														disabled={isLoading}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							</div>

							{/* Rôle et statut */}
							<div className="border-t pt-6">
								<h3 className="text-lg font-medium mb-4">Rôle et statut</h3>

								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<FormField
										control={form.control}
										name="role"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Rôle *</FormLabel>
												<Select
													onValueChange={field.onChange}
													value={field.value}
												>
													<FormControl>
														<SelectTrigger disabled={isLoading}>
															<SelectValue placeholder="Sélectionnez un rôle" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														<SelectItem value="membre">Membre</SelectItem>
														<SelectItem value="tresorier">Trésorier</SelectItem>
														<SelectItem value="admin">
															Administrateur
														</SelectItem>
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="statut"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Statut *</FormLabel>
												<Select
													onValueChange={field.onChange}
													value={field.value}
												>
													<FormControl>
														<SelectTrigger disabled={isLoading}>
															<SelectValue placeholder="Sélectionnez un statut" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														<SelectItem value="actif">Actif</SelectItem>
														<SelectItem value="en_attente">
															En attente
														</SelectItem>
														<SelectItem value="suspendu">Suspendu</SelectItem>
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							</div>

							{/* Message d'erreur */}
							{error && (
								<div className="text-red-600 text-sm bg-red-50 p-3 rounded">
									{error}
								</div>
							)}

							{/* Actions */}
							<div className="flex justify-end gap-4 pt-6">
								<Link href="/members">
									<Button variant="outline" disabled={isLoading}>
										Annuler
									</Button>
								</Link>
								<Button type="submit" disabled={isLoading}>
									{isLoading ? "Création..." : "Créer le membre"}
								</Button>
							</div>
						</form>
					</Form>
				</CardContent>
			</Card>
		</div>
	);
}
