{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card'\n      className={cn(\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-header'\n      className={cn(\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-title'\n      className={cn('leading-none font-semibold', className)}\n      {...props}\n    />\n  );\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-description'\n      className={cn('text-muted-foreground text-sm', className)}\n      {...props}\n    />\n  );\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-action'\n      className={cn(\n        'col-start-2 row-span-2 row-start-1 self-start justify-self-end',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-content'\n      className={cn('px-6', className)}\n      {...props}\n    />\n  );\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-footer'\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\n      {...props}\n    />\n  );\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EACX,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EACX,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EACX,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EAAC,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/label.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\n\nimport { cn } from '@/lib/utils';\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot='label'\n      className={cn(\n        'flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nexport { Label };\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,wUAAC,qSAAmB;QAClB,aAAU;QACV,WAAW,IAAA,wIAAE,EACX,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/form.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\nimport { Slot } from '@radix-ui/react-slot';\nimport {\n  Controller,\n  FormProvider,\n  useFormContext,\n  useFormState,\n  type ControllerProps,\n  type FieldPath,\n  type FieldValues\n} from 'react-hook-form';\n\nimport { cn } from '@/lib/utils';\nimport { Label } from '@/components/ui/label';\n\nconst Form = FormProvider;\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\n> = {\n  name: TName;\n};\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\n  {} as FormFieldContextValue\n);\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  );\n};\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext);\n  const itemContext = React.useContext(FormItemContext);\n  const { getFieldState } = useFormContext();\n  const formState = useFormState({ name: fieldContext.name });\n  const fieldState = getFieldState(fieldContext.name, formState);\n\n  if (!fieldContext) {\n    throw new Error('useFormField should be used within <FormField>');\n  }\n\n  const { id } = itemContext;\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState\n  };\n};\n\ntype FormItemContextValue = {\n  id: string;\n};\n\nconst FormItemContext = React.createContext<FormItemContextValue>(\n  {} as FormItemContextValue\n);\n\nfunction FormItem({ className, ...props }: React.ComponentProps<'div'>) {\n  const id = React.useId();\n\n  return (\n    <FormItemContext.Provider value={{ id }}>\n      <div\n        data-slot='form-item'\n        className={cn('grid gap-2', className)}\n        {...props}\n      />\n    </FormItemContext.Provider>\n  );\n}\n\nfunction FormLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  const { error, formItemId } = useFormField();\n\n  return (\n    <Label\n      data-slot='form-label'\n      data-error={!!error}\n      className={cn('data-[error=true]:text-destructive', className)}\n      htmlFor={formItemId}\n      {...props}\n    />\n  );\n}\n\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\n  const { error, formItemId, formDescriptionId, formMessageId } =\n    useFormField();\n\n  return (\n    <Slot\n      data-slot='form-control'\n      id={formItemId}\n      aria-describedby={\n        !error\n          ? `${formDescriptionId}`\n          : `${formDescriptionId} ${formMessageId}`\n      }\n      aria-invalid={!!error}\n      {...props}\n    />\n  );\n}\n\nfunction FormDescription({ className, ...props }: React.ComponentProps<'p'>) {\n  const { formDescriptionId } = useFormField();\n\n  return (\n    <p\n      data-slot='form-description'\n      id={formDescriptionId}\n      className={cn('text-muted-foreground text-sm', className)}\n      {...props}\n    />\n  );\n}\n\nfunction FormMessage({ className, ...props }: React.ComponentProps<'p'>) {\n  const { error, formMessageId } = useFormField();\n  const body = error ? String(error?.message ?? '') : props.children;\n\n  if (!body) {\n    return null;\n  }\n\n  return (\n    <p\n      data-slot='form-message'\n      id={formMessageId}\n      className={cn('text-destructive text-sm', className)}\n      {...props}\n    />\n  );\n}\n\nexport {\n  useFormField,\n  Form,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n  FormField\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;;;AAhBA;;;;;;AAkBA,MAAM,OAAO,sRAAY;AASzB,MAAM,iCAAmB,yTAAmB,CAC1C,CAAC;AAGH,MAAM,YAAY;QAGhB,EACA,GAAG,OACkC;IACrC,qBACE,wUAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,wUAAC,oRAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;KAXM;AAaN,MAAM,eAAe;;IACnB,MAAM,eAAe,sTAAgB,CAAC;IACtC,MAAM,cAAc,sTAAgB,CAAC;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,IAAA,wRAAc;IACxC,MAAM,YAAY,IAAA,sRAAY,EAAC;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,AAAC,GAAK,OAAH,IAAG;QAClB,mBAAmB,AAAC,GAAK,OAAH,IAAG;QACzB,eAAe,AAAC,GAAK,OAAH,IAAG;QACrB,GAAG,UAAU;IACf;AACF;GArBM;;QAGsB,wRAAc;QACtB,sRAAY;;;AAuBhC,MAAM,gCAAkB,yTAAmB,CACzC,CAAC;AAGH,SAAS,SAAS,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;;IAChB,MAAM,KAAK,iTAAW;IAEtB,qBACE,wUAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,wUAAC;YACC,aAAU;YACV,WAAW,IAAA,wIAAE,EAAC,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;IAZS;MAAA;AAcT,SAAS,UAAU,KAGgC;QAHhC,EACjB,SAAS,EACT,GAAG,OAC8C,GAHhC;;IAIjB,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,wUAAC,yJAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,IAAA,wIAAE,EAAC,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;IAfS;;QAIuB;;;MAJvB;AAiBT,SAAS,YAAY,KAA+C;QAA/C,EAAE,GAAG,OAA0C,GAA/C;;IACnB,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAC3D;IAEF,qBACE,wUAAC,4TAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,AAAC,GAAoB,OAAlB,qBACH,AAAC,GAAuB,OAArB,mBAAkB,KAAiB,OAAd;QAE9B,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;IAjBS;;QAEL;;;MAFK;AAmBT,SAAS,gBAAgB,KAAkD;QAAlD,EAAE,SAAS,EAAE,GAAG,OAAkC,GAAlD;;IACvB,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,wUAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,IAAA,wIAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;IAXS;;QACuB;;;MADvB;AAaT,SAAS,YAAY,KAAkD;QAAlD,EAAE,SAAS,EAAE,GAAG,OAAkC,GAAlD;;IACnB,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;QACL;IAA5B,MAAM,OAAO,QAAQ,OAAO,CAAA,iBAAA,kBAAA,4BAAA,MAAO,OAAO,cAAd,4BAAA,iBAAkB,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,wUAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,IAAA,wIAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;IAhBS;;QAC0B;;;MAD1B", "debugId": null}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,wUAAC,sSAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,wUAAC,uSAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,wUAAC,uSAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ,GAPsB;IAQrB,qBACE,wUAAC,ySAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,IAAA,wIAAE,EACX,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,wUAAC,sSAAoB;gBAAC,OAAO;0BAC3B,cAAA,wUAAC,8UAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,KAKgC;QALhC,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD,GALhC;IAMrB,qBACE,wUAAC,wSAAsB;kBACrB,cAAA,wUAAC,ySAAuB;YACtB,aAAU;YACV,WAAW,IAAA,wIAAE,EACX,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,wUAAC;;;;;8BACD,wUAAC,0SAAwB;oBACvB,WAAW,IAAA,wIAAE,EACX,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,wUAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,wUAAC,uSAAqB;QACpB,aAAU;QACV,WAAW,IAAA,wIAAE,EAAC,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,KAIgC;QAJhC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C,GAJhC;IAKlB,qBACE,wUAAC,sSAAoB;QACnB,aAAU;QACV,WAAW,IAAA,wIAAE,EACX,6aACA;QAED,GAAG,KAAK;;0BAET,wUAAC;gBAAK,WAAU;0BACd,cAAA,wUAAC,+SAA6B;8BAC5B,cAAA,wUAAC,wTAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,wUAAC,0SAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,SAAS,EACT,GAAG,OACoD,GAHhC;IAIvB,qBACE,wUAAC,2SAAyB;QACxB,aAAU;QACV,WAAW,IAAA,wIAAE,EAAC,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,wUAAC,gTAA8B;QAC7B,aAAU;QACV,WAAW,IAAA,wIAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,wUAAC,wUAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,wUAAC,kTAAgC;QAC/B,aAAU;QACV,WAAW,IAAA,wIAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,wUAAC,8UAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 646, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\n\nimport { cn } from \"@/lib/utils\"\nimport { buttonVariants } from \"@/components/ui/button\"\n\nconst AlertDialog = AlertDialogPrimitive.Root\n\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\n\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\n\nconst AlertDialogOverlay = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\n\nconst AlertDialogContent = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPortal>\n    <AlertDialogOverlay />\n    <AlertDialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    />\n  </AlertDialogPortal>\n))\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\n\nconst AlertDialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\n\nconst AlertDialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\n\nconst AlertDialogTitle = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold\", className)}\n    {...props}\n  />\n))\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\n\nconst AlertDialogDescription = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nAlertDialogDescription.displayName =\n  AlertDialogPrimitive.Description.displayName\n\nconst AlertDialogAction = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Action\n    ref={ref}\n    className={cn(buttonVariants(), className)}\n    {...props}\n  />\n))\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\n\nconst AlertDialogCancel = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Cancel\n    ref={ref}\n    className={cn(\n      buttonVariants({ variant: \"outline\" }),\n      \"mt-2 sm:mt-0\",\n      className\n    )}\n    {...props}\n  />\n))\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\n\nexport {\n  AlertDialog,\n  AlertDialogPortal,\n  AlertDialogOverlay,\n  AlertDialogTrigger,\n  AlertDialogContent,\n  AlertDialogHeader,\n  AlertDialogFooter,\n  AlertDialogTitle,\n  AlertDialogDescription,\n  AlertDialogAction,\n  AlertDialogCancel,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQA,MAAM,cAAc,ySAAyB;AAE7C,MAAM,qBAAqB,4SAA4B;AAEvD,MAAM,oBAAoB,2SAA2B;AAErD,MAAM,mCAAqB,sTAAgB,CAGzC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,wUAAC,4SAA4B;QAC3B,WAAW,IAAA,wIAAE,EACX,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;;KAVH;AAaN,mBAAmB,WAAW,GAAG,4SAA4B,CAAC,WAAW;AAEzE,MAAM,mCAAqB,sTAAgB,OAGzC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,wUAAC;;0BACC,wUAAC;;;;;0BACD,wUAAC,4SAA4B;gBAC3B,KAAK;gBACL,WAAW,IAAA,wIAAE,EACX,+fACA;gBAED,GAAG,KAAK;;;;;;;;;;;;;;AAIf,mBAAmB,WAAW,GAAG,4SAA4B,CAAC,WAAW;AAEzE,MAAM,oBAAoB;QAAC,EACzB,SAAS,EACT,GAAG,OACkC;yBACrC,wUAAC;QACC,WAAW,IAAA,wIAAE,EACX,oDACA;QAED,GAAG,KAAK;;;;;;;MATP;AAYN,kBAAkB,WAAW,GAAG;AAEhC,MAAM,oBAAoB;QAAC,EACzB,SAAS,EACT,GAAG,OACkC;yBACrC,wUAAC;QACC,WAAW,IAAA,wIAAE,EACX,iEACA;QAED,GAAG,KAAK;;;;;;;MATP;AAYN,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,sTAAgB,OAGvC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,wUAAC,0SAA0B;QACzB,KAAK;QACL,WAAW,IAAA,wIAAE,EAAC,yBAAyB;QACtC,GAAG,KAAK;;;;;;;;AAGb,iBAAiB,WAAW,GAAG,0SAA0B,CAAC,WAAW;AAErE,MAAM,uCAAyB,sTAAgB,OAG7C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,wUAAC,gTAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,wIAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,uBAAuB,WAAW,GAChC,gTAAgC,CAAC,WAAW;AAE9C,MAAM,kCAAoB,sTAAgB,OAGxC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,wUAAC,2SAA2B;QAC1B,KAAK;QACL,WAAW,IAAA,wIAAE,EAAC,IAAA,mKAAc,KAAI;QAC/B,GAAG,KAAK;;;;;;;;AAGb,kBAAkB,WAAW,GAAG,2SAA2B,CAAC,WAAW;AAEvE,MAAM,kCAAoB,sTAAgB,QAGxC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,wUAAC,2SAA2B;QAC1B,KAAK;QACL,WAAW,IAAA,wIAAE,EACX,IAAA,mKAAc,EAAC;YAAE,SAAS;QAAU,IACpC,gBACA;QAED,GAAG,KAAK;;;;;;;;AAGb,kBAAkB,WAAW,GAAG,2SAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 831, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/lib/api.ts"], "sourcesContent": ["// Configuration de l'API\nexport const API_BASE_URL =\n\tprocess.env.NEXT_PUBLIC_API_URL || \"http://localhost:4000\";\n\n// Types pour l'authentification\nexport interface LoginRequest {\n\tusername: string;\n\tpassword: string;\n}\n\nexport interface LoginResponse {\n\taccess_token: string;\n\tuser: {\n\t\tid: string;\n\t\tusername: string;\n\t\tnom: string;\n\t\tprenom: string;\n\t\temail: string;\n\t\trole: \"admin\" | \"tresorier\" | \"membre\";\n\t\tstatut: \"actif\" | \"en_attente\" | \"suspendu\";\n\t};\n}\n\nexport interface RegisterRequest {\n\tusername: string;\n\tpassword: string;\n\tnom: string;\n\tprenom: string;\n\temail: string;\n\ttelephone: string;\n\trole: \"admin\" | \"tresorier\" | \"membre\";\n\tstatut?: \"actif\" | \"en_attente\" | \"suspendu\";\n}\n\nexport interface ApiError {\n\tmessage: string;\n\tstatusCode: number;\n\terror?: string;\n}\n\n// Service API\nexport class ApiService {\n\tprivate baseUrl: string;\n\n\tconstructor(baseUrl: string = API_BASE_URL) {\n\t\tthis.baseUrl = baseUrl;\n\t}\n\n\tprivate async request<T>(\n\t\tendpoint: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> {\n\t\tconst url = `${this.baseUrl}${endpoint}`;\n\n\t\tconst config: RequestInit = {\n\t\t\theaders: {\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\t...options.headers,\n\t\t\t},\n\t\t\t...options,\n\t\t};\n\n\t\ttry {\n\t\t\tconst response = await fetch(url, config);\n\n\t\t\tif (!response.ok) {\n\t\t\t\tconst errorData: ApiError = await response.json().catch(() => ({\n\t\t\t\t\tmessage: \"Une erreur est survenue\",\n\t\t\t\t\tstatusCode: response.status,\n\t\t\t\t}));\n\t\t\t\tthrow new Error(errorData.message || `HTTP ${response.status}`);\n\t\t\t}\n\n\t\t\treturn await response.json();\n\t\t} catch (error) {\n\t\t\tconsole.log(error);\n\n\t\t\tif (error instanceof Error) {\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t\tthrow new Error(\"Erreur de connexion au serveur\");\n\t\t}\n\t}\n\n\t// Authentification\n\tasync login(credentials: LoginRequest): Promise<LoginResponse> {\n\t\treturn this.request<LoginResponse>(\"/auth/login\", {\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(credentials),\n\t\t});\n\t}\n\n\tasync register(\n\t\tuserData: RegisterRequest,\n\t): Promise<{ message: string; user: any }> {\n\t\treturn this.request<{ message: string; user: any }>(\"/auth/register\", {\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(userData),\n\t\t});\n\t}\n\n\t// Méthodes avec authentification\n\tasync authenticatedRequest<T>(\n\t\tendpoint: string,\n\t\ttoken: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> {\n\t\treturn this.request<T>(endpoint, {\n\t\t\t...options,\n\t\t\theaders: {\n\t\t\t\t...options.headers,\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\tAuthorization: `Bearer ${token}`,\n\t\t\t},\n\t\t});\n\t}\n\n\t// Utilisateurs\n\tasync getUsers(token: string): Promise<any[]> {\n\t\treturn this.authenticatedRequest<any[]>(\"/users\", token);\n\t}\n\n\tasync getUser(id: string, token: string): Promise<any> {\n\t\treturn this.authenticatedRequest<any>(`/users/${id}`, token);\n\t}\n}\n\n// Instance par défaut\nexport const apiService = new ApiService();\n"], "names": [], "mappings": "AAAA,yBAAyB;;;;;;;;;AAExB;;;AADM,MAAM,eACZ,sTAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AAuC7B,MAAM;IAOZ,MAAc,QACb,QAAgB,EAEH;YADb,UAAA,iEAAuB,CAAC;QAExB,MAAM,MAAM,AAAC,GAAiB,OAAf,IAAI,CAAC,OAAO,EAAY,OAAT;QAE9B,MAAM,SAAsB;YAC3B,SAAS;gBACR,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACnB;YACA,GAAG,OAAO;QACX;QAEA,IAAI;YACH,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBACjB,MAAM,YAAsB,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;wBAC9D,SAAS;wBACT,YAAY,SAAS,MAAM;oBAC5B,CAAC;gBACD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,AAAC,QAAuB,OAAhB,SAAS,MAAM;YAC7D;YAEA,OAAO,MAAM,SAAS,IAAI;QAC3B,EAAE,OAAO,OAAO;YACf,QAAQ,GAAG,CAAC;YAEZ,IAAI,iBAAiB,OAAO;gBAC3B,MAAM;YACP;YACA,MAAM,IAAI,MAAM;QACjB;IACD;IAEA,mBAAmB;IACnB,MAAM,MAAM,WAAyB,EAA0B;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAgB,eAAe;YACjD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACtB;IACD;IAEA,MAAM,SACL,QAAyB,EACiB;QAC1C,OAAO,IAAI,CAAC,OAAO,CAAiC,kBAAkB;YACrE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACtB;IACD;IAEA,iCAAiC;IACjC,MAAM,qBACL,QAAgB,EAChB,KAAa,EAEA;YADb,UAAA,iEAAuB,CAAC;QAExB,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAChC,GAAG,OAAO;YACV,SAAS;gBACR,GAAG,QAAQ,OAAO;gBAClB,gBAAgB;gBAChB,eAAe,AAAC,UAAe,OAAN;YAC1B;QACD;IACD;IAEA,eAAe;IACf,MAAM,SAAS,KAAa,EAAkB;QAC7C,OAAO,IAAI,CAAC,oBAAoB,CAAQ,UAAU;IACnD;IAEA,MAAM,QAAQ,EAAU,EAAE,KAAa,EAAgB;QACtD,OAAO,IAAI,CAAC,oBAAoB,CAAM,AAAC,UAAY,OAAH,KAAM;IACvD;IAhFA,YAAY,UAAkB,YAAY,CAAE;QAF5C,yPAAQ,WAAR,KAAA;QAGC,IAAI,CAAC,OAAO,GAAG;IAChB;AA+ED;AAGO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 918, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/hooks/use-api.ts"], "sourcesContent": ["import { useSession } from 'next-auth/react';\nimport { apiService } from '@/lib/api';\n\nexport function useApi() {\n  const { data: session } = useSession();\n\n  const authenticatedRequest = async <T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<T> => {\n    if (!session?.accessToken) {\n      throw new Error('Non authentifié');\n    }\n\n    return apiService.authenticatedRequest<T>(\n      endpoint,\n      session.accessToken,\n      options\n    );\n  };\n\n  return {\n    // Méthodes d'authentification (pas besoin de token)\n    login: apiService.login.bind(apiService),\n    register: apiService.register.bind(apiService),\n    \n    // Méthodes authentifiées\n    authenticatedRequest,\n    \n    // Raccourcis pour les endpoints courants\n    getUsers: () => authenticatedRequest<any[]>('/users'),\n    getUser: (id: string) => authenticatedRequest<any>(`/users/${id}`),\n    createUser: (userData: any) => authenticatedRequest<any>('/users', {\n      method: 'POST',\n      body: JSON.stringify(userData),\n    }),\n    updateUser: (id: string, userData: any) => authenticatedRequest<any>(`/users/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(userData),\n    }),\n    deleteUser: (id: string) => authenticatedRequest<any>(`/users/${id}`, {\n      method: 'DELETE',\n    }),\n    \n    // Sessions/Exercices\n    getSessions: () => authenticatedRequest<any[]>('/sessions'),\n    getSession: (id: string) => authenticatedRequest<any>(`/sessions/${id}`),\n    createSession: (sessionData: any) => authenticatedRequest<any>('/sessions', {\n      method: 'POST',\n      body: JSON.stringify(sessionData),\n    }),\n    \n    // Caisses\n    getCaisses: () => authenticatedRequest<any[]>('/caisses'),\n    getCaisse: (id: string) => authenticatedRequest<any>(`/caisses/${id}`),\n    createCaisse: (caisseData: any) => authenticatedRequest<any>('/caisses', {\n      method: 'POST',\n      body: JSON.stringify(caisseData),\n    }),\n    \n    // Réunions\n    getReunions: () => authenticatedRequest<any[]>('/reunions'),\n    getReunion: (id: string) => authenticatedRequest<any>(`/reunions/${id}`),\n  };\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEO,SAAS;;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,8QAAU;IAEpC,MAAM,uBAAuB,eAC3B;YACA,2EAAuB,CAAC;QAExB,IAAI,EAAC,oBAAA,8BAAA,QAAS,WAAW,GAAE;YACzB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,8IAAU,CAAC,oBAAoB,CACpC,UACA,QAAQ,WAAW,EACnB;IAEJ;IAEA,OAAO;QACL,oDAAoD;QACpD,OAAO,8IAAU,CAAC,KAAK,CAAC,IAAI,CAAC,8IAAU;QACvC,UAAU,8IAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,8IAAU;QAE7C,yBAAyB;QACzB;QAEA,yCAAyC;QACzC,UAAU,IAAM,qBAA4B;QAC5C,SAAS,CAAC,KAAe,qBAA0B,AAAC,UAAY,OAAH;QAC7D,YAAY,CAAC,WAAkB,qBAA0B,UAAU;gBACjE,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACvB;QACA,YAAY,CAAC,IAAY,WAAkB,qBAA0B,AAAC,UAAY,OAAH,KAAM;gBACnF,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACvB;QACA,YAAY,CAAC,KAAe,qBAA0B,AAAC,UAAY,OAAH,KAAM;gBACpE,QAAQ;YACV;QAEA,qBAAqB;QACrB,aAAa,IAAM,qBAA4B;QAC/C,YAAY,CAAC,KAAe,qBAA0B,AAAC,aAAe,OAAH;QACnE,eAAe,CAAC,cAAqB,qBAA0B,aAAa;gBAC1E,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACvB;QAEA,UAAU;QACV,YAAY,IAAM,qBAA4B;QAC9C,WAAW,CAAC,KAAe,qBAA0B,AAAC,YAAc,OAAH;QACjE,cAAc,CAAC,aAAoB,qBAA0B,YAAY;gBACvE,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACvB;QAEA,WAAW;QACX,aAAa,IAAM,qBAA4B;QAC/C,YAAY,CAAC,KAAe,qBAA0B,AAAC,aAAe,OAAH;IACrE;AACF;GA7DgB;;QACY,8QAAU", "debugId": null}}, {"offset": {"line": 988, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/app/dashboard/members/%5Bid%5D/edit/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { use<PERSON><PERSON><PERSON>, useRouter } from \"next/navigation\";\nimport { useSession } from \"next-auth/react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { Arrow<PERSON>ef<PERSON>, Trash2 } from \"lucide-react\";\nimport Link from \"next/link\";\n\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport {\n\tCard,\n\tCardContent,\n\tCardDescription,\n\tCardHeader,\n\tCardTitle,\n} from \"@/components/ui/card\";\nimport {\n\tForm,\n\tFormControl,\n\tFormField,\n\tFormItem,\n\tFormLabel,\n\tFormMessage,\n} from \"@/components/ui/form\";\nimport {\n\tSelect,\n\tSelectContent,\n\tSelectItem,\n\tSelectTrigger,\n\tSelectValue,\n} from \"@/components/ui/select\";\nimport {\n\tAlertDialog,\n\tAlertDialogAction,\n\tAlertDialogCancel,\n\tAlertDialogContent,\n\tAlertDialogDescription,\n\t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n\t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n\tAlertDialog<PERSON><PERSON>le,\n\tAlertDialog<PERSON>rigger,\n} from \"@/components/ui/alert-dialog\";\nimport { useApi } from \"@/hooks/use-api\";\n\nconst editMemberSchema = z.object({\n\tusername: z\n\t\t.string()\n\t\t.min(3, \"Le nom d'utilisateur doit contenir au moins 3 caractères\"),\n\tpassword: z.string().optional(),\n\tnom: z.string().min(2, \"Le nom doit contenir au moins 2 caractères\"),\n\tprenom: z.string().min(2, \"Le prénom doit contenir au moins 2 caractères\"),\n\temail: z.string().email(\"Adresse email invalide\"),\n\ttelephone: z\n\t\t.string()\n\t\t.min(8, \"Le numéro de téléphone doit contenir au moins 8 caractères\"),\n\trole: z.enum([\"admin\", \"tresorier\", \"membre\"]),\n\tstatut: z.enum([\"actif\", \"en_attente\", \"suspendu\"]),\n});\n\ntype EditMemberForm = z.infer<typeof editMemberSchema>;\n\ninterface Member {\n\t_id: string;\n\tusername: string;\n\tnom: string;\n\tprenom: string;\n\temail: string;\n\ttelephone: string;\n\trole: \"admin\" | \"tresorier\" | \"membre\";\n\tstatut: \"actif\" | \"en_attente\" | \"suspendu\";\n}\nexport default function EditMemberPage() {\n\tconst { id: memberId } = useParams();\n\tconst { data: session } = useSession();\n\tconst router = useRouter();\n\tconst api = useApi();\n\n\tconst [member, setMember] = useState<Member | null>(null);\n\tconst [isLoading, setIsLoading] = useState(false);\n\tconst [isDeleting, setIsDeleting] = useState(false);\n\tconst [error, setError] = useState<string | null>(null);\n\tconst [loading, setLoading] = useState(true);\n\n\t// Vérifier les permissions\n\tconst canEditMembers =\n\t\tsession?.user && (session.user as any).role === \"admin\";\n\n\tconst form = useForm<EditMemberForm>({\n\t\tresolver: zodResolver(editMemberSchema),\n\t\tdefaultValues: {\n\t\t\tusername: \"\",\n\t\t\tpassword: \"\",\n\t\t\tnom: \"\",\n\t\t\tprenom: \"\",\n\t\t\temail: \"\",\n\t\t\ttelephone: \"\",\n\t\t\trole: \"membre\",\n\t\t\tstatut: \"actif\",\n\t\t},\n\t});\n\n\tuseEffect(() => {\n\t\tif (session?.accessToken && memberId) {\n\t\t\tloadMember();\n\t\t}\n\t}, [session, memberId]);\n\n\tconst loadMember = async () => {\n\t\ttry {\n\t\t\tsetLoading(true);\n\t\t\tconst memberData = await api.authenticatedRequest<Member>(\n\t\t\t\t`/users/${memberId}`,\n\t\t\t);\n\t\t\tsetMember(memberData);\n\n\t\t\t// Remplir le formulaire avec les données existantes\n\t\t\tform.reset({\n\t\t\t\tusername: memberData.username,\n\t\t\t\tpassword: \"\", // Ne pas pré-remplir le mot de passe\n\t\t\t\tnom: memberData.nom,\n\t\t\t\tprenom: memberData.prenom,\n\t\t\t\temail: memberData.email,\n\t\t\t\ttelephone: memberData.telephone,\n\t\t\t\trole: memberData.role,\n\t\t\t\tstatut: memberData.statut,\n\t\t\t});\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Erreur lors du chargement du membre:\", error);\n\t\t\tsetError(\"Membre non trouvé\");\n\t\t} finally {\n\t\t\tsetLoading(false);\n\t\t}\n\t};\n\n\tconst onSubmit = async (data: EditMemberForm) => {\n\t\tsetIsLoading(true);\n\t\tsetError(null);\n\n\t\ttry {\n\t\t\t// Supprimer le mot de passe du payload s'il est vide\n\t\t\tconst updateData = { ...data };\n\t\t\tif (!updateData.password) {\n\t\t\t\tdelete updateData.password;\n\t\t\t}\n\n\t\t\tawait api.authenticatedRequest(`/users/${memberId}`, {\n\t\t\t\tmethod: \"PATCH\",\n\n\t\t\t\tbody: JSON.stringify(updateData),\n\t\t\t});\n\n\t\t\t// Rediriger vers la liste des membres\n\t\t\trouter.push(\"/dashboard/members\");\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Erreur lors de la mise à jour du membre:\", error);\n\t\t\tif (error instanceof Error) {\n\t\t\t\tsetError(error.message);\n\t\t\t} else {\n\t\t\t\tsetError(\n\t\t\t\t\t\"Erreur lors de la mise à jour du membre. Veuillez réessayer.\",\n\t\t\t\t);\n\t\t\t}\n\t\t} finally {\n\t\t\tsetIsLoading(false);\n\t\t}\n\t};\n\n\tconst handleDelete = async () => {\n\t\tsetIsDeleting(true);\n\t\ttry {\n\t\t\tawait api.authenticatedRequest(`/users/${memberId}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t});\n\t\t\trouter.push(\"/dashboard/members\");\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Erreur lors de la suppression:\", error);\n\t\t\tsetError(\"Erreur lors de la suppression du membre\");\n\t\t} finally {\n\t\t\tsetIsDeleting(false);\n\t\t}\n\t};\n\n\tif (!canEditMembers) {\n\t\treturn (\n\t\t\t<div className=\"space-y-6\">\n\t\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t\t<Link href=\"/dashboard/members\">\n\t\t\t\t\t\t<Button variant=\"ghost\" size=\"sm\">\n\t\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\t\tRetour\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</Link>\n\t\t\t\t</div>\n\t\t\t\t<div className=\"flex items-center justify-center h-64\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<h2 className=\"text-lg font-semibold text-gray-900\">\n\t\t\t\t\t\t\tAccès refusé\n\t\t\t\t\t\t</h2>\n\t\t\t\t\t\t<p className=\"text-gray-600\">\n\t\t\t\t\t\t\tSeuls les administrateurs peuvent modifier des membres.\n\t\t\t\t\t\t</p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t);\n\t}\n\n\tif (loading) {\n\t\treturn (\n\t\t\t<div className=\"space-y-6\">\n\t\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t\t<Link href=\"/dashboard/members\">\n\t\t\t\t\t\t<Button variant=\"ghost\" size=\"sm\">\n\t\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\t\tRetour\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</Link>\n\t\t\t\t</div>\n\t\t\t\t<div className=\"flex justify-center py-8\">\n\t\t\t\t\t<div className=\"text-gray-500\">Chargement...</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t);\n\t}\n\n\tif (error && !member) {\n\t\treturn (\n\t\t\t<div className=\"space-y-6\">\n\t\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t\t<Link href=\"/dashboard/members\">\n\t\t\t\t\t\t<Button variant=\"ghost\" size=\"sm\">\n\t\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\t\tRetour\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</Link>\n\t\t\t\t</div>\n\t\t\t\t<div className=\"flex items-center justify-center h-64\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<h2 className=\"text-lg font-semibold text-gray-900\">Erreur</h2>\n\t\t\t\t\t\t<p className=\"text-gray-600\">{error}</p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t);\n\t}\n\n\treturn (\n\t\t<div className=\"space-y-6\">\n\t\t\t{/* En-tête */}\n\t\t\t<div className=\"flex items-center justify-between\">\n\t\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t\t<Link href=\"/dashboard/members\">\n\t\t\t\t\t\t<Button variant=\"ghost\" size=\"sm\">\n\t\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\t\tRetour\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</Link>\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<h1 className=\"text-2xl font-bold text-gray-900\">\n\t\t\t\t\t\t\tModifier {member?.prenom} {member?.nom}\n\t\t\t\t\t\t</h1>\n\t\t\t\t\t\t<p className=\"text-gray-600\">Modifier les informations du membre</p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\n\t\t\t\t{/* Bouton de suppression */}\n\t\t\t\t<AlertDialog>\n\t\t\t\t\t<AlertDialogTrigger asChild>\n\t\t\t\t\t\t<Button variant=\"destructive\" size=\"sm\">\n\t\t\t\t\t\t\t<Trash2 className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\t\tSupprimer\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</AlertDialogTrigger>\n\t\t\t\t\t<AlertDialogContent>\n\t\t\t\t\t\t<AlertDialogHeader>\n\t\t\t\t\t\t\t<AlertDialogTitle>Confirmer la suppression</AlertDialogTitle>\n\t\t\t\t\t\t\t<AlertDialogDescription>\n\t\t\t\t\t\t\t\tÊtes-vous sûr de vouloir supprimer ce membre ? Cette action est\n\t\t\t\t\t\t\t\tirréversible.\n\t\t\t\t\t\t\t</AlertDialogDescription>\n\t\t\t\t\t\t</AlertDialogHeader>\n\t\t\t\t\t\t<AlertDialogFooter>\n\t\t\t\t\t\t\t<AlertDialogCancel>Annuler</AlertDialogCancel>\n\t\t\t\t\t\t\t<AlertDialogAction\n\t\t\t\t\t\t\t\tonClick={handleDelete}\n\t\t\t\t\t\t\t\tdisabled={isDeleting}\n\t\t\t\t\t\t\t\tclassName=\"bg-red-600 hover:bg-red-700\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{isDeleting ? \"Suppression...\" : \"Supprimer\"}\n\t\t\t\t\t\t\t</AlertDialogAction>\n\t\t\t\t\t\t</AlertDialogFooter>\n\t\t\t\t\t</AlertDialogContent>\n\t\t\t\t</AlertDialog>\n\t\t\t</div>\n\n\t\t\t{/* Formulaire */}\n\t\t\t<Card className=\"max-w-2xl\">\n\t\t\t\t<CardHeader>\n\t\t\t\t\t<CardTitle>Informations du membre</CardTitle>\n\t\t\t\t\t<CardDescription>Modifiez les informations du membre</CardDescription>\n\t\t\t\t</CardHeader>\n\t\t\t\t<CardContent>\n\t\t\t\t\t<Form {...form}>\n\t\t\t\t\t\t<form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n\t\t\t\t\t\t\t{/* Informations personnelles */}\n\t\t\t\t\t\t\t<div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\tname=\"prenom\"\n\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Prénom *</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t<Input\n\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"Prénom\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t{...field}\n\t\t\t\t\t\t\t\t\t\t\t\t\tdisabled={isLoading}\n\t\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\tname=\"nom\"\n\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Nom *</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t<Input\n\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"Nom de famille\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t{...field}\n\t\t\t\t\t\t\t\t\t\t\t\t\tdisabled={isLoading}\n\t\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t{/* Informations de contact */}\n\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\tname=\"email\"\n\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t<FormLabel>Email *</FormLabel>\n\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t<Input\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"email\"\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"<EMAIL>\"\n\t\t\t\t\t\t\t\t\t\t\t\t{...field}\n\t\t\t\t\t\t\t\t\t\t\t\tdisabled={isLoading}\n\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\tname=\"telephone\"\n\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t<FormLabel>Téléphone *</FormLabel>\n\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t<Input\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"+237123456789\"\n\t\t\t\t\t\t\t\t\t\t\t\t{...field}\n\t\t\t\t\t\t\t\t\t\t\t\tdisabled={isLoading}\n\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t{/* Informations de connexion */}\n\t\t\t\t\t\t\t<div className=\"border-t pt-6\">\n\t\t\t\t\t\t\t\t<h3 className=\"text-lg font-medium mb-4\">\n\t\t\t\t\t\t\t\t\tInformations de connexion\n\t\t\t\t\t\t\t\t</h3>\n\n\t\t\t\t\t\t\t\t<div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n\t\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\t\tname=\"username\"\n\t\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Nom d'utilisateur *</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Input\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"nom_utilisateur\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{...field}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisabled={isLoading}\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\t\tname=\"password\"\n\t\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Nouveau mot de passe</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Input\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"password\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"Laisser vide pour ne pas changer\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{...field}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisabled={isLoading}\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t{/* Rôle et statut */}\n\t\t\t\t\t\t\t<div className=\"border-t pt-6\">\n\t\t\t\t\t\t\t\t<h3 className=\"text-lg font-medium mb-4\">Rôle et statut</h3>\n\n\t\t\t\t\t\t\t\t<div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n\t\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\t\tname=\"role\"\n\t\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Rôle *</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t\t<Select\n\t\t\t\t\t\t\t\t\t\t\t\t\tonValueChange={field.onChange}\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={field.value}\n\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectTrigger disabled={isLoading}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectValue />\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectTrigger>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectItem value=\"membre\">Membre</SelectItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectItem value=\"tresorier\">Trésorier</SelectItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectItem value=\"admin\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tAdministrateur\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t\t</Select>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\t\tname=\"statut\"\n\t\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Statut *</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t\t<Select\n\t\t\t\t\t\t\t\t\t\t\t\t\tonValueChange={field.onChange}\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={field.value}\n\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectTrigger disabled={isLoading}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectValue />\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectTrigger>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectItem value=\"actif\">Actif</SelectItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectItem value=\"en_attente\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tEn attente\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectItem value=\"suspendu\">Suspendu</SelectItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t\t</Select>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t{/* Message d'erreur */}\n\t\t\t\t\t\t\t{error && (\n\t\t\t\t\t\t\t\t<div className=\"text-red-600 text-sm bg-red-50 p-3 rounded\">\n\t\t\t\t\t\t\t\t\t{error}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t)}\n\n\t\t\t\t\t\t\t{/* Actions */}\n\t\t\t\t\t\t\t<div className=\"flex justify-end gap-4 pt-6\">\n\t\t\t\t\t\t\t\t<Link href=\"/dashboard/members\">\n\t\t\t\t\t\t\t\t\t<Button variant=\"outline\" disabled={isLoading}>\n\t\t\t\t\t\t\t\t\t\tAnnuler\n\t\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t\t</Link>\n\t\t\t\t\t\t\t\t<Button type=\"submit\" disabled={isLoading}>\n\t\t\t\t\t\t\t\t\t{isLoading ? \"Mise à jour...\" : \"Mettre à jour\"}\n\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</form>\n\t\t\t\t\t</Form>\n\t\t\t\t</CardContent>\n\t\t\t</Card>\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAOA;AAQA;AAOA;AAWA;;;AA9CA;;;;;;;;;;;;;;;;AAgDA,MAAM,mBAAmB,iPAAC,CAAC,MAAM,CAAC;IACjC,UAAU,iPAAC,CACT,MAAM,GACN,GAAG,CAAC,GAAG;IACT,UAAU,iPAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,KAAK,iPAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACvB,QAAQ,iPAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,OAAO,iPAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,WAAW,iPAAC,CACV,MAAM,GACN,GAAG,CAAC,GAAG;IACT,MAAM,iPAAC,CAAC,IAAI,CAAC;QAAC;QAAS;QAAa;KAAS;IAC7C,QAAQ,iPAAC,CAAC,IAAI,CAAC;QAAC;QAAS;QAAc;KAAW;AACnD;AAce,SAAS;;IACvB,MAAM,EAAE,IAAI,QAAQ,EAAE,GAAG,IAAA,6RAAS;IAClC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,8QAAU;IACpC,MAAM,SAAS,IAAA,6RAAS;IACxB,MAAM,MAAM,IAAA,mJAAM;IAElB,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,oTAAQ,EAAgB;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,oTAAQ,EAAC;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,oTAAQ,EAAC;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,oTAAQ,EAAgB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,oTAAQ,EAAC;IAEvC,2BAA2B;IAC3B,MAAM,iBACL,CAAA,oBAAA,8BAAA,QAAS,IAAI,KAAI,AAAC,QAAQ,IAAI,CAAS,IAAI,KAAK;IAEjD,MAAM,OAAO,IAAA,iRAAO,EAAiB;QACpC,UAAU,IAAA,mSAAW,EAAC;QACtB,eAAe;YACd,UAAU;YACV,UAAU;YACV,KAAK;YACL,QAAQ;YACR,OAAO;YACP,WAAW;YACX,MAAM;YACN,QAAQ;QACT;IACD;IAEA,IAAA,qTAAS;oCAAC;YACT,IAAI,CAAA,oBAAA,8BAAA,QAAS,WAAW,KAAI,UAAU;gBACrC;YACD;QACD;mCAAG;QAAC;QAAS;KAAS;IAEtB,MAAM,aAAa;QAClB,IAAI;YACH,WAAW;YACX,MAAM,aAAa,MAAM,IAAI,oBAAoB,CAChD,AAAC,UAAkB,OAAT;YAEX,UAAU;YAEV,oDAAoD;YACpD,KAAK,KAAK,CAAC;gBACV,UAAU,WAAW,QAAQ;gBAC7B,UAAU;gBACV,KAAK,WAAW,GAAG;gBACnB,QAAQ,WAAW,MAAM;gBACzB,OAAO,WAAW,KAAK;gBACvB,WAAW,WAAW,SAAS;gBAC/B,MAAM,WAAW,IAAI;gBACrB,QAAQ,WAAW,MAAM;YAC1B;QACD,EAAE,OAAO,OAAO;YACf,QAAQ,KAAK,CAAC,wCAAwC;YACtD,SAAS;QACV,SAAU;YACT,WAAW;QACZ;IACD;IAEA,MAAM,WAAW,OAAO;QACvB,aAAa;QACb,SAAS;QAET,IAAI;YACH,qDAAqD;YACrD,MAAM,aAAa;gBAAE,GAAG,IAAI;YAAC;YAC7B,IAAI,CAAC,WAAW,QAAQ,EAAE;gBACzB,OAAO,WAAW,QAAQ;YAC3B;YAEA,MAAM,IAAI,oBAAoB,CAAC,AAAC,UAAkB,OAAT,WAAY;gBACpD,QAAQ;gBAER,MAAM,KAAK,SAAS,CAAC;YACtB;YAEA,sCAAsC;YACtC,OAAO,IAAI,CAAC;QACb,EAAE,OAAO,OAAO;YACf,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,IAAI,iBAAiB,OAAO;gBAC3B,SAAS,MAAM,OAAO;YACvB,OAAO;gBACN,SACC;YAEF;QACD,SAAU;YACT,aAAa;QACd;IACD;IAEA,MAAM,eAAe;QACpB,cAAc;QACd,IAAI;YACH,MAAM,IAAI,oBAAoB,CAAC,AAAC,UAAkB,OAAT,WAAY;gBACpD,QAAQ;YACT;YACA,OAAO,IAAI,CAAC;QACb,EAAE,OAAO,OAAO;YACf,QAAQ,KAAK,CAAC,kCAAkC;YAChD,SAAS;QACV,SAAU;YACT,cAAc;QACf;IACD;IAEA,IAAI,CAAC,gBAAgB;QACpB,qBACC,wUAAC;YAAI,WAAU;;8BACd,wUAAC;oBAAI,WAAU;8BACd,cAAA,wUAAC,qTAAI;wBAAC,MAAK;kCACV,cAAA,wUAAC,2JAAM;4BAAC,SAAQ;4BAAQ,MAAK;;8CAC5B,wUAAC,gUAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;8BAKzC,wUAAC;oBAAI,WAAU;8BACd,cAAA,wUAAC;wBAAI,WAAU;;0CACd,wUAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,wUAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;IAOlC;IAEA,IAAI,SAAS;QACZ,qBACC,wUAAC;YAAI,WAAU;;8BACd,wUAAC;oBAAI,WAAU;8BACd,cAAA,wUAAC,qTAAI;wBAAC,MAAK;kCACV,cAAA,wUAAC,2JAAM;4BAAC,SAAQ;4BAAQ,MAAK;;8CAC5B,wUAAC,gUAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;8BAKzC,wUAAC;oBAAI,WAAU;8BACd,cAAA,wUAAC;wBAAI,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAInC;IAEA,IAAI,SAAS,CAAC,QAAQ;QACrB,qBACC,wUAAC;YAAI,WAAU;;8BACd,wUAAC;oBAAI,WAAU;8BACd,cAAA,wUAAC,qTAAI;wBAAC,MAAK;kCACV,cAAA,wUAAC,2JAAM;4BAAC,SAAQ;4BAAQ,MAAK;;8CAC5B,wUAAC,gUAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;8BAKzC,wUAAC;oBAAI,WAAU;8BACd,cAAA,wUAAC;wBAAI,WAAU;;0CACd,wUAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,wUAAC;gCAAE,WAAU;0CAAiB;;;;;;;;;;;;;;;;;;;;;;;IAKnC;IAEA,qBACC,wUAAC;QAAI,WAAU;;0BAEd,wUAAC;gBAAI,WAAU;;kCACd,wUAAC;wBAAI,WAAU;;0CACd,wUAAC,qTAAI;gCAAC,MAAK;0CACV,cAAA,wUAAC,2JAAM;oCAAC,SAAQ;oCAAQ,MAAK;;sDAC5B,wUAAC,gUAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAIxC,wUAAC;;kDACA,wUAAC;wCAAG,WAAU;;4CAAmC;4CACtC,mBAAA,6BAAA,OAAQ,MAAM;4CAAC;4CAAE,mBAAA,6BAAA,OAAQ,GAAG;;;;;;;kDAEvC,wUAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;kCAK/B,wUAAC,yKAAW;;0CACX,wUAAC,gLAAkB;gCAAC,OAAO;0CAC1B,cAAA,wUAAC,2JAAM;oCAAC,SAAQ;oCAAc,MAAK;;sDAClC,wUAAC,uTAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAIrC,wUAAC,gLAAkB;;kDAClB,wUAAC,+KAAiB;;0DACjB,wUAAC,8KAAgB;0DAAC;;;;;;0DAClB,wUAAC,oLAAsB;0DAAC;;;;;;;;;;;;kDAKzB,wUAAC,+KAAiB;;0DACjB,wUAAC,+KAAiB;0DAAC;;;;;;0DACnB,wUAAC,+KAAiB;gDACjB,SAAS;gDACT,UAAU;gDACV,WAAU;0DAET,aAAa,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQtC,wUAAC,uJAAI;gBAAC,WAAU;;kCACf,wUAAC,6JAAU;;0CACV,wUAAC,4JAAS;0CAAC;;;;;;0CACX,wUAAC,kKAAe;0CAAC;;;;;;;;;;;;kCAElB,wUAAC,8JAAW;kCACX,cAAA,wUAAC,uJAAI;4BAAE,GAAG,IAAI;sCACb,cAAA,wUAAC;gCAAK,UAAU,KAAK,YAAY,CAAC;gCAAW,WAAU;;kDAEtD,wUAAC;wCAAI,WAAU;;0DACd,wUAAC,4JAAS;gDACT,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ;wDAAC,EAAE,KAAK,EAAE;yEACjB,wUAAC,2JAAQ;;0EACR,wUAAC,4JAAS;0EAAC;;;;;;0EACX,wUAAC,8JAAW;0EACX,cAAA,wUAAC,yJAAK;oEACL,aAAY;oEACX,GAAG,KAAK;oEACT,UAAU;;;;;;;;;;;0EAGZ,wUAAC,8JAAW;;;;;;;;;;;;;;;;;0DAIf,wUAAC,4JAAS;gDACT,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ;wDAAC,EAAE,KAAK,EAAE;yEACjB,wUAAC,2JAAQ;;0EACR,wUAAC,4JAAS;0EAAC;;;;;;0EACX,wUAAC,8JAAW;0EACX,cAAA,wUAAC,yJAAK;oEACL,aAAY;oEACX,GAAG,KAAK;oEACT,UAAU;;;;;;;;;;;0EAGZ,wUAAC,8JAAW;;;;;;;;;;;;;;;;;;;;;;;kDAOhB,wUAAC,4JAAS;wCACT,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ;gDAAC,EAAE,KAAK,EAAE;iEACjB,wUAAC,2JAAQ;;kEACR,wUAAC,4JAAS;kEAAC;;;;;;kEACX,wUAAC,8JAAW;kEACX,cAAA,wUAAC,yJAAK;4DACL,MAAK;4DACL,aAAY;4DACX,GAAG,KAAK;4DACT,UAAU;;;;;;;;;;;kEAGZ,wUAAC,8JAAW;;;;;;;;;;;;;;;;;kDAKf,wUAAC,4JAAS;wCACT,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ;gDAAC,EAAE,KAAK,EAAE;iEACjB,wUAAC,2JAAQ;;kEACR,wUAAC,4JAAS;kEAAC;;;;;;kEACX,wUAAC,8JAAW;kEACX,cAAA,wUAAC,yJAAK;4DACL,aAAY;4DACX,GAAG,KAAK;4DACT,UAAU;;;;;;;;;;;kEAGZ,wUAAC,8JAAW;;;;;;;;;;;;;;;;;kDAMf,wUAAC;wCAAI,WAAU;;0DACd,wUAAC;gDAAG,WAAU;0DAA2B;;;;;;0DAIzC,wUAAC;gDAAI,WAAU;;kEACd,wUAAC,4JAAS;wDACT,SAAS,KAAK,OAAO;wDACrB,MAAK;wDACL,QAAQ;gEAAC,EAAE,KAAK,EAAE;iFACjB,wUAAC,2JAAQ;;kFACR,wUAAC,4JAAS;kFAAC;;;;;;kFACX,wUAAC,8JAAW;kFACX,cAAA,wUAAC,yJAAK;4EACL,aAAY;4EACX,GAAG,KAAK;4EACT,UAAU;;;;;;;;;;;kFAGZ,wUAAC,8JAAW;;;;;;;;;;;;;;;;;kEAIf,wUAAC,4JAAS;wDACT,SAAS,KAAK,OAAO;wDACrB,MAAK;wDACL,QAAQ;gEAAC,EAAE,KAAK,EAAE;iFACjB,wUAAC,2JAAQ;;kFACR,wUAAC,4JAAS;kFAAC;;;;;;kFACX,wUAAC,8JAAW;kFACX,cAAA,wUAAC,yJAAK;4EACL,MAAK;4EACL,aAAY;4EACX,GAAG,KAAK;4EACT,UAAU;;;;;;;;;;;kFAGZ,wUAAC,8JAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQjB,wUAAC;wCAAI,WAAU;;0DACd,wUAAC;gDAAG,WAAU;0DAA2B;;;;;;0DAEzC,wUAAC;gDAAI,WAAU;;kEACd,wUAAC,4JAAS;wDACT,SAAS,KAAK,OAAO;wDACrB,MAAK;wDACL,QAAQ;gEAAC,EAAE,KAAK,EAAE;iFACjB,wUAAC,2JAAQ;;kFACR,wUAAC,4JAAS;kFAAC;;;;;;kFACX,wUAAC,2JAAM;wEACN,eAAe,MAAM,QAAQ;wEAC7B,OAAO,MAAM,KAAK;;0FAElB,wUAAC,8JAAW;0FACX,cAAA,wUAAC,kKAAa;oFAAC,UAAU;8FACxB,cAAA,wUAAC,gKAAW;;;;;;;;;;;;;;;0FAGd,wUAAC,kKAAa;;kGACb,wUAAC,+JAAU;wFAAC,OAAM;kGAAS;;;;;;kGAC3B,wUAAC,+JAAU;wFAAC,OAAM;kGAAY;;;;;;kGAC9B,wUAAC,+JAAU;wFAAC,OAAM;kGAAQ;;;;;;;;;;;;;;;;;;kFAK5B,wUAAC,8JAAW;;;;;;;;;;;;;;;;;kEAIf,wUAAC,4JAAS;wDACT,SAAS,KAAK,OAAO;wDACrB,MAAK;wDACL,QAAQ;gEAAC,EAAE,KAAK,EAAE;iFACjB,wUAAC,2JAAQ;;kFACR,wUAAC,4JAAS;kFAAC;;;;;;kFACX,wUAAC,2JAAM;wEACN,eAAe,MAAM,QAAQ;wEAC7B,OAAO,MAAM,KAAK;;0FAElB,wUAAC,8JAAW;0FACX,cAAA,wUAAC,kKAAa;oFAAC,UAAU;8FACxB,cAAA,wUAAC,gKAAW;;;;;;;;;;;;;;;0FAGd,wUAAC,kKAAa;;kGACb,wUAAC,+JAAU;wFAAC,OAAM;kGAAQ;;;;;;kGAC1B,wUAAC,+JAAU;wFAAC,OAAM;kGAAa;;;;;;kGAG/B,wUAAC,+JAAU;wFAAC,OAAM;kGAAW;;;;;;;;;;;;;;;;;;kFAG/B,wUAAC,8JAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAQhB,uBACA,wUAAC;wCAAI,WAAU;kDACb;;;;;;kDAKH,wUAAC;wCAAI,WAAU;;0DACd,wUAAC,qTAAI;gDAAC,MAAK;0DACV,cAAA,wUAAC,2JAAM;oDAAC,SAAQ;oDAAU,UAAU;8DAAW;;;;;;;;;;;0DAIhD,wUAAC,2JAAM;gDAAC,MAAK;gDAAS,UAAU;0DAC9B,YAAY,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzC;GAxbwB;;QACE,6RAAS;QACR,8QAAU;QACrB,6RAAS;QACZ,mJAAM;QAYL,iRAAO;;;KAhBG", "debugId": null}}]}