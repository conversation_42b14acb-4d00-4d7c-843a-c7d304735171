"use client";

import { useSession } from "next-auth/react";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Users, DollarSign, TrendingUp, Activity } from "lucide-react";

const stats = [
	{
		title: "Total Members",
		value: "24",
		description: "Active tontine members",
		icon: Users,
		color: "text-blue-600",
		bgColor: "bg-blue-100",
	},
	{
		title: "Total Contributions",
		value: "$12,450",
		description: "This month",
		icon: DollarSign,
		color: "text-green-600",
		bgColor: "bg-green-100",
	},
	{
		title: "Growth",
		value: "+12.5%",
		description: "From last month",
		icon: TrendingUp,
		color: "text-purple-600",
		bgColor: "bg-purple-100",
	},
	{
		title: "Active Cycles",
		value: "3",
		description: "Currently running",
		icon: Activity,
		color: "text-orange-600",
		bgColor: "bg-orange-100",
	},
];

export default function DashboardPage() {
	const { data: session } = useSession();

	return (
		<div className="space-y-6">
			{/* Welcome section */}
			<div>
				<h1 className="text-2xl font-bold text-gray-900">
					Bienvenue, {session?.user?.name}!
				</h1>
				<p className="text-gray-600 mt-1">
					Voici un aperçu de votre tontine aujourd'hui.
				</p>
				{session?.user && (
					<div className="mt-2 text-sm text-gray-500">
						Connecté en tant que{" "}
						<span className="font-medium">{(session.user as any).role}</span> (
						{(session.user as any).username})
					</div>
				)}
			</div>

			{/* Stats grid */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
				{stats.map((stat) => {
					const Icon = stat.icon;
					return (
						<Card key={stat.title}>
							<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
								<CardTitle className="text-sm font-medium text-gray-600">
									{stat.title}
								</CardTitle>
								<div className={`p-2 rounded-full ${stat.bgColor}`}>
									<Icon className={`h-4 w-4 ${stat.color}`} />
								</div>
							</CardHeader>
							<CardContent>
								<div className="text-2xl font-bold text-gray-900">
									{stat.value}
								</div>
								<p className="text-xs text-gray-600 mt-1">{stat.description}</p>
							</CardContent>
						</Card>
					);
				})}
			</div>

			{/* Recent activity */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				<Card>
					<CardHeader>
						<CardTitle>Recent Contributions</CardTitle>
						<CardDescription>
							Latest member contributions to the tontine
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							{[
								{ name: "Alice Johnson", amount: "$500", time: "2 hours ago" },
								{ name: "Bob Smith", amount: "$500", time: "5 hours ago" },
								{ name: "Carol Davis", amount: "$500", time: "1 day ago" },
								{ name: "David Wilson", amount: "$500", time: "2 days ago" },
							].map((contribution, index) => (
								<div key={index} className="flex items-center justify-between">
									<div>
										<p className="text-sm font-medium text-gray-900">
											{contribution.name}
										</p>
										<p className="text-xs text-gray-500">{contribution.time}</p>
									</div>
									<div className="text-sm font-medium text-green-600">
										{contribution.amount}
									</div>
								</div>
							))}
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Upcoming Payouts</CardTitle>
						<CardDescription>Next scheduled member payouts</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							{[
								{ name: "Emma Brown", amount: "$12,000", date: "Jan 15, 2025" },
								{
									name: "Frank Miller",
									amount: "$12,000",
									date: "Feb 15, 2025",
								},
								{ name: "Grace Lee", amount: "$12,000", date: "Mar 15, 2025" },
							].map((payout, index) => (
								<div key={index} className="flex items-center justify-between">
									<div>
										<p className="text-sm font-medium text-gray-900">
											{payout.name}
										</p>
										<p className="text-xs text-gray-500">{payout.date}</p>
									</div>
									<div className="text-sm font-medium text-blue-600">
										{payout.amount}
									</div>
								</div>
							))}
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
