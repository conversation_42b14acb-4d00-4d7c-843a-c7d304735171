import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { Session } from '../../sessions/schemas/session.schema';
import { User } from '../../users/schemas/user.schema';

export enum CaisseType {
  PRINCIPALE = 'PRINCIPALE',
  REUNION = 'REUNION',
}

export type CaisseDocument = HydratedDocument<Caisse>;

@Schema({ timestamps: true })
export class Caisse {
  @Prop({ required: true })
  nom!: string;
 
  @Prop({ required: true, enum: [CaisseType.PRINCIPALE, CaisseType.REUNION] })
  type!: CaisseType;

  @Prop({ type: Number, default: 0 })
  soldeActuel!: number;

  // Only for caisses liées à une réunion (nullable otherwise)
  @Prop({ type: Types.ObjectId, ref: 'Session', required: false })
  sessionId?: Types.ObjectId;

  // creator
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  createdBy!: Types.ObjectId;

  // If type === REUNION, it must be linked to a Reunion
  // @Prop({ type: Types.ObjectId, ref: 'Reunion', required: false })
  // reunionId?: Types.ObjectId;

  // For REUNION type, linked principal caisse for emargement
  @Prop({ type: Types.ObjectId, ref: 'Caisse', required: false })
  caissePrincipaleId?: Types.ObjectId;
}

export const CaisseSchema = SchemaFactory.createForClass(Caisse);