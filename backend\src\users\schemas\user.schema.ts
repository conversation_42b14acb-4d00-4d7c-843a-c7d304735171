import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";

export type UserDocument = User & Document;

export enum UserRole {
	ADMIN = "admin",
	TRESORIER = "tresorier",
	MEMBRE = "membre",
}

export enum UserStatus {
	ACTIF = "actif",
	EN_ATTENTE = "en_attente",
	SUSPENDU = "suspendu",
}

@Schema({ timestamps: true })
export class User {
	@Prop({ required: true, unique: true })
	username!: string;

	@Prop({ required: true })
	password!: string; // stored hashed

	@Prop({ required: true })
	nom!: string;

	@Prop({ required: true })
	prenom!: string;

	@Prop({ required: true, unique: true })
	email!: string;

	@Prop({ required: true })
	telephone!: string;

	@Prop({
		type: String,
		enum: [UserRole.ADMIN, UserRole.TRESORIER, UserRole.MEMBRE],
		required: true,
	})
	role!: UserRole;

	@Prop({
		type: String,
		enum: [UserStatus.ACTIF, UserStatus.EN_ATTENTE, UserStatus.SUSPENDU],
		default: UserStatus.ACTIF,
	})
	statut!: UserStatus;
}

export const UserSchema = SchemaFactory.createForClass(User);
