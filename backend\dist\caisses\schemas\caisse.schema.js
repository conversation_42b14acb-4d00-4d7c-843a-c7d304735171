"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CaisseSchema = exports.Caisse = exports.CaisseType = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
var CaisseType;
(function (CaisseType) {
    CaisseType["PRINCIPALE"] = "PRINCIPALE";
    CaisseType["REUNION"] = "REUNION";
})(CaisseType || (exports.CaisseType = CaisseType = {}));
let Caisse = class Caisse {
};
exports.Caisse = Caisse;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Caisse.prototype, "nom", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, enum: [CaisseType.PRINCIPALE, CaisseType.REUNION] }),
    __metadata("design:type", String)
], Caisse.prototype, "type", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Number, default: 0 }),
    __metadata("design:type", Number)
], Caisse.prototype, "soldeActuel", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Types.ObjectId, ref: 'Session', required: false }),
    __metadata("design:type", mongoose_2.Types.ObjectId)
], Caisse.prototype, "sessionId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Types.ObjectId, ref: 'User', required: true }),
    __metadata("design:type", mongoose_2.Types.ObjectId)
], Caisse.prototype, "createdBy", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Types.ObjectId, ref: 'Caisse', required: false }),
    __metadata("design:type", mongoose_2.Types.ObjectId)
], Caisse.prototype, "caissePrincipaleId", void 0);
exports.Caisse = Caisse = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], Caisse);
exports.CaisseSchema = mongoose_1.SchemaFactory.createForClass(Caisse);
