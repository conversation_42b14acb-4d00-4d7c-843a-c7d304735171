# 🔐 Configuration de l'Authentification - To<PERSON>e MVP

## 📋 Vue d'ensemble

Le système d'authentification est maintenant complètement fonctionnel avec :
- **Backend** : NestJS + JWT + MongoDB
- **Frontend** : Next.js + NextAuth.js
- **R<PERSON>les** : Admin, Trésorier, Membre
- **Statuts** : Actif, En attente, Suspendu

## 🚀 Démarrage rapide

### 1. <PERSON><PERSON><PERSON><PERSON> le Backend
```bash
cd backend
npm install
npm run build
npm start
```
Le backend sera disponible sur `http://localhost:3000`

### 2. D<PERSON><PERSON>rer le Frontend
```bash
cd frontend
npm install
npm run dev
```
Le frontend sera disponible sur `http://localhost:3001`

### 3. Tester l'authentification
```bash
# Depuis la racine du projet
node test-auth-flow.js
```

## 🎭 Rôles et Permissions

### Admin
- **Permissions** : Accès complet à toutes les fonctionnalités
- **Peut** : <PERSON><PERSON>er/modifier/supprimer utilisateurs, sessions, caisses, réunions
- **Endpoints** : Tous les endpoints

### Trésorier
- **Permissions** : Gestion financière et consultation
- **Peut** : Consulter utilisateurs, gérer caisses, émarger, consulter sessions/réunions
- **Endpoints** : GET /users, CRUD /caisses, POST /caisses/:id/emarger, GET /sessions, GET /reunions

### Membre
- **Permissions** : Consultation uniquement
- **Peut** : Consulter sessions et réunions
- **Endpoints** : GET /sessions, GET /reunions

## 📝 Utilisation

### Page d'inscription (Temporaire)
- **URL** : `http://localhost:3001/auth/register`
- **Note** : Cette page sera supprimée en production
- **Utilisation** : Créer des comptes de test pendant le développement

### Page de connexion
- **URL** : `http://localhost:3001/auth/signin`
- **Champs** : Nom d'utilisateur, Mot de passe

### Dashboard
- **URL** : `http://localhost:3001/dashboard`
- **Accès** : Après connexion réussie
- **Affiche** : Informations utilisateur et rôle

## 🔧 Structure des données

### Utilisateur
```typescript
{
  username: string;      // Unique
  password: string;      // Hashé avec bcrypt
  nom: string;
  prenom: string;
  email: string;         // Unique
  telephone: string;
  role: 'admin' | 'tresorier' | 'membre';
  statut: 'actif' | 'en_attente' | 'suspendu';
}
```

### Session JWT
```typescript
{
  user: {
    id: string;
    name: string;
    email: string;
    username: string;
    role: 'admin' | 'tresorier' | 'membre';
    statut: 'actif' | 'en_attente' | 'suspendu';
  };
  accessToken: string;
}
```

## 🛠️ API Endpoints

### Authentification
- `POST /auth/register` - Inscription (temporaire)
- `POST /auth/login` - Connexion

### Utilisateurs
- `GET /users` - Liste des utilisateurs (Admin + Trésorier)
- `GET /users/:id` - Détails utilisateur (Admin + Trésorier)
- `POST /users` - Créer utilisateur (Admin)
- `PATCH /users/:id` - Modifier utilisateur (Admin)
- `DELETE /users/:id` - Supprimer utilisateur (Admin)

### Sessions
- `GET /sessions` - Liste des sessions (Tous)
- `POST /sessions` - Créer session (Admin)
- `PATCH /sessions/:id` - Modifier session (Admin)
- `DELETE /sessions/:id` - Supprimer session (Admin)

### Caisses
- `GET /caisses` - Liste des caisses (Admin + Trésorier)
- `POST /caisses` - Créer caisse (Admin + Trésorier)
- `PATCH /caisses/:id` - Modifier caisse (Admin + Trésorier)
- `DELETE /caisses/:id` - Supprimer caisse (Admin)
- `POST /caisses/:id/emarger` - Émarger (Trésorier)

### Réunions
- `GET /reunions` - Liste des réunions (Tous)
- `PATCH /reunions/:id` - Modifier réunion (Admin)

## 🧪 Tests

### Comptes de test suggérés
```javascript
// Admin
{
  username: 'admin',
  password: 'admin123',
  nom: 'Administrateur',
  prenom: 'Super',
  email: '<EMAIL>',
  role: 'admin'
}

// Trésorier
{
  username: 'tresorier',
  password: 'tresorier123',
  nom: 'Trésorier',
  prenom: 'Chef',
  email: '<EMAIL>',
  role: 'tresorier'
}

// Membre
{
  username: 'membre',
  password: 'membre123',
  nom: 'Membre',
  prenom: 'Simple',
  email: '<EMAIL>',
  role: 'membre'
}
```

## 🔄 Prochaines étapes

1. **Gestion des membres** - Interface CRUD complète
2. **Gestion des sessions** - Création et suivi des exercices
3. **Gestion des caisses** - Suivi financier
4. **Tableau de bord** - Données réelles et statistiques
5. **Suppression de la page d'inscription** - Seul l'admin pourra créer des comptes

## 🐛 Dépannage

### Backend ne démarre pas
- Vérifier que MongoDB est en cours d'exécution
- Vérifier les variables d'environnement
- Vérifier que le port 3000 est libre

### Frontend ne se connecte pas
- Vérifier que le backend est démarré
- Vérifier l'URL de l'API dans `frontend/src/lib/api.ts`
- Vérifier les logs de la console navigateur

### Erreurs d'authentification
- Vérifier que l'utilisateur existe
- Vérifier le mot de passe
- Vérifier les logs backend pour plus de détails
