import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { CaissesController } from './caisses.controller';
import { CaissesService } from './caisses.service';
import { Caisse, CaisseSchema } from './schemas/caisse.schema';

@Module({
  imports: [MongooseModule.forFeature([{ name: Caisse.name, schema: CaisseSchema }])],
  controllers: [CaissesController],
  providers: [CaissesService],
  exports: [CaissesService],
})
export class CaissesModule {}