import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { Session } from '../../sessions/schemas/session.schema';
import { Caisse } from '../../caisses/schemas/caisse.schema';

export type ReunionDocument = HydratedDocument<Reunion>;

@Schema({ timestamps: true })
export class Reunion {
  @Prop({ type: Date, required: true })
  dateReunion!: Date;

  @Prop({ type: String, required: false })
  lieu?: string;

  @Prop({ type: Types.ObjectId, ref: 'Caisse', required: false })
  caissePrincipale?: Types.ObjectId; // reference to a principal caisse

  @Prop({ type: Types.ObjectId, ref: 'Session', required: true })
  sessionId!: Types.ObjectId;
}

export const ReunionSchema = SchemaFactory.createForClass(Reunion);