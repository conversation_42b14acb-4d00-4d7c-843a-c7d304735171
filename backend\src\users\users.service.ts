import { Injectable, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as bcrypt from 'bcryptjs';

import { User, UserDocument, UserRole, UserStatus } from './schemas/user.schema';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserStatsDto } from './dto/user-stats.dto';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
  ) {}
  async create(createUserData: CreateUserDto): Promise<UserDocument> {
    const { username, password, nom, prenom, email, telephone, role, statut } = createUserData;

    // Vérifier si le nom d'utilisateur existe déjà
    const existingUser = await this.userModel.findOne({ username });
    if (existingUser) {
      throw new ConflictException('Ce nom d\'utilisateur est déjà utilisé.');
    }

    // Vérifier si l'email existe déjà
    const existingEmail = await this.userModel.findOne({ email });
    if (existingEmail) {
      throw new ConflictException('Cette adresse email est déjà utilisée.');
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    const createdUser = new this.userModel({
      username,
      password: hashedPassword,
      nom,
      prenom,
      email,
      telephone,
      role,
      statut: statut || 'actif', // Statut par défaut
    });

    return createdUser.save();
  }
  async findByUsername(username: string): Promise<User | null> {
    return this.userModel.findOne({ username }).exec();
  }
  async findById(id: string): Promise<User | null> {
    return this.userModel.findById(id ).exec();
  }
  async getUserId(username: string): Promise<String | unknown> {
    const user = await this.userModel.findOne({ username }).exec();
    if(user) {
      return user._id;
    }
  }
  async validateUser(username: string, pass: string): Promise<User | null> {
    const user = await this.findByUsername(username);
    if (!user) return null;

    const isPasswordValid = await bcrypt.compare(pass, user.password);
    if (!isPasswordValid) return null;

    return user;
  }

  async findAll(role?: UserRole, statut?: UserStatus, search?: string): Promise<User[]> {
    const filter: any = {};

    // Filtrer par rôle si spécifié
    if (role) {
      filter.role = role;
    }

    // Filtrer par statut si spécifié
    if (statut) {
      filter.statut = statut;
    }

    // Recherche textuelle si spécifiée
    if (search) {
      filter.$or = [
        { nom: { $regex: search, $options: 'i' } },
        { prenom: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { username: { $regex: search, $options: 'i' } },
      ];
    }

    return this.userModel.find(filter).exec();
  }

  async findOne(id: string): Promise<User | null> {
    return this.userModel.findById(id).exec();
  }

  async update(id: string, updateUserData: UpdateUserDto): Promise<User | null> {
    // Vérifier si l'utilisateur existe
    const existingUser = await this.userModel.findById(id);
    if (!existingUser) {
      throw new ConflictException('Utilisateur non trouvé.');
    }

    // Vérifier l'unicité du nom d'utilisateur si modifié
    if (updateUserData.username && updateUserData.username !== existingUser.username) {
      const userWithSameUsername = await this.userModel.findOne({ username: updateUserData.username });
      if (userWithSameUsername) {
        throw new ConflictException('Ce nom d\'utilisateur est déjà utilisé.');
      }
    }

    // Vérifier l'unicité de l'email si modifié
    if (updateUserData.email && updateUserData.email !== existingUser.email) {
      const userWithSameEmail = await this.userModel.findOne({ email: updateUserData.email });
      if (userWithSameEmail) {
        throw new ConflictException('Cette adresse email est déjà utilisée.');
      }
    }

    let data: Partial<UpdateUserDto> = { ...updateUserData };

    // Hasher le mot de passe s'il est fourni
    if (updateUserData.password) {
      data.password = await bcrypt.hash(updateUserData.password, 10);
    }
    
    return this.userModel.findByIdAndUpdate(id, data, { new: true }).exec();
  }

  async remove(id: string): Promise<User | null> {
    return this.userModel.findByIdAndDelete(id).exec();
  }

  // Méthodes spécialisées pour la gestion des membres

  async updateStatut(id: string, statut: UserStatus): Promise<User | null> {
    return this.userModel.findByIdAndUpdate(
      id,
      { statut },
      { new: true }
    ).exec();
  }

  async findByRole(role: UserRole): Promise<User[]> {
    return this.userModel.find({ role }).exec();
  }

  async findByStatut(statut: UserStatus): Promise<User[]> {
    return this.userModel.find({ statut }).exec();
  }

  async getStats(): Promise<UserStatsDto> {
    const totalUsers = await this.userModel.countDocuments();

    // Statistiques par rôle
    const statsByRole = await this.userModel.aggregate([
      {
        $group: {
          _id: '$role',
          count: { $sum: 1 }
        }
      }
    ]);

    // Statistiques par statut
    const statsByStatus = await this.userModel.aggregate([
      {
        $group: {
          _id: '$statut',
          count: { $sum: 1 }
        }
      }
    ]);

    // Membres actifs par rôle
    const activeByRole = await this.userModel.aggregate([
      {
        $match: { statut: UserStatus.ACTIF }
      },
      {
        $group: {
          _id: '$role',
          count: { $sum: 1 }
        }
      }
    ]);

    return {
      total: totalUsers,
      byRole: statsByRole.reduce((acc, item) => {
        acc[item._id] = item.count;
        return acc;
      }, {}),
      byStatus: statsByStatus.reduce((acc, item) => {
        acc[item._id] = item.count;
        return acc;
      }, {}),
      activeByRole: activeByRole.reduce((acc, item) => {
        acc[item._id] = item.count;
        return acc;
      }, {}),
    };
  }
}

