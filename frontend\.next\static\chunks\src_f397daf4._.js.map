{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,uOAAO,EAAC,IAAA,iMAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\n\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\n        destructive:\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\n        outline:\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\n        secondary:\n          'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\n        ghost:\n          'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\n        link: 'text-primary underline-offset-4 hover:underline'\n      },\n      size: {\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\n        icon: 'size-9'\n      }\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default'\n    }\n  }\n);\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'button'> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? Slot : 'button';\n\n  return (\n    <Comp\n      data-slot='button'\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,IAAA,qPAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,gTAAI,GAAG;IAE9B,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nfunction Input({ className, type, ...props }: React.ComponentProps<'input'>) {\n  return (\n    <input\n      type={type}\n      data-slot='input'\n      className={cn(\n        'file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\n        'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]',\n        'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nexport { Input };\n"], "names": [], "mappings": ";;;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,4TAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card'\n      className={cn(\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-header'\n      className={cn(\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-title'\n      className={cn('leading-none font-semibold', className)}\n      {...props}\n    />\n  );\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-description'\n      className={cn('text-muted-foreground text-sm', className)}\n      {...props}\n    />\n  );\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-action'\n      className={cn(\n        'col-start-2 row-span-2 row-start-1 self-start justify-self-end',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-content'\n      className={cn('px-6', className)}\n      {...props}\n    />\n  );\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-footer'\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\n      {...props}\n    />\n  );\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACb,qBACE,4TAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,4TAAC;YACC,aAAU;YACV,WAAW,IAAA,4HAAE,EAAC,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACnB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACjB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACnB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IAChB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACjB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACjB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,KAGY;QAHZ,EACpB,SAAS,EACT,GAAG,OAC6B,GAHZ;IAIpB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 400, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,KAEoC;QAFpC,EACpB,GAAG,OACqD,GAFpC;IAGpB,qBAAO,4TAAC,8RAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,KAEgC;QAFhC,EAC1B,GAAG,OACuD,GAFhC;IAG1B,qBACE,4TAAC,gSAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,KAEgC;QAFhC,EAC3B,GAAG,OACwD,GAFhC;IAG3B,qBACE,4TAAC,iSAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,KAIgC;QAJhC,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD,GAJhC;IAK3B,qBACE,4TAAC,gSAA4B;kBAC3B,cAAA,4TAAC,iSAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,IAAA,4HAAE,EACX,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,KAEgC;QAFhC,EACzB,GAAG,OACsD,GAFhC;IAGzB,qBACE,4TAAC,+RAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,KAQzB;QARyB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ,GARyB;IASxB,qBACE,4TAAC,8RAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,IAAA,4HAAE,EACX,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,KAKgC;QALhC,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D,GALhC;IAMhC,qBACE,4TAAC,sSAAkC;QACjC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,4TAAC;gBAAK,WAAU;0BACd,cAAA,4TAAC,uSAAmC;8BAClC,cAAA,4TAAC,4SAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,KAEgC;QAFhC,EAC9B,GAAG,OAC2D,GAFhC;IAG9B,qBACE,4TAAC,oSAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,KAIgC;QAJhC,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D,GAJhC;IAK7B,qBACE,4TAAC,mSAA+B;QAC9B,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,gTACA;QAED,GAAG,KAAK;;0BAET,4TAAC;gBAAK,WAAU;0BACd,cAAA,4TAAC,uSAAmC;8BAClC,cAAA,4TAAC,+SAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,KAM1B;QAN0B,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ,GAN0B;IAOzB,qBACE,4TAAC,+RAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,IAAA,4HAAE,EACX,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,KAGgC;QAHhC,EAC7B,SAAS,EACT,GAAG,OAC0D,GAHhC;IAI7B,qBACE,4TAAC,mSAA+B;QAC9B,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,KAGC;QAHD,EAC5B,SAAS,EACT,GAAG,OAC0B,GAHD;IAI5B,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,KAEgC;QAFhC,EACvB,GAAG,OACoD,GAFhC;IAGvB,qBAAO,4TAAC,6RAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,KAO/B;QAP+B,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ,GAP+B;IAQ9B,qBACE,4TAAC,oSAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,IAAA,4HAAE,EACX,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,4TAAC,qUAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,4TAAC,oSAAgC;QAC/B,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 724, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,4TAAC,0RAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,4TAAC,2RAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,4TAAC,2RAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ,GAPsB;IAQrB,qBACE,4TAAC,6RAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,IAAA,4HAAE,EACX,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,4TAAC,0RAAoB;gBAAC,OAAO;0BAC3B,cAAA,4TAAC,kUAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,KAKgC;QALhC,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD,GALhC;IAMrB,qBACE,4TAAC,4RAAsB;kBACrB,cAAA,4TAAC,6RAAuB;YACtB,aAAU;YACV,WAAW,IAAA,4HAAE,EACX,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,4TAAC;;;;;8BACD,4TAAC,8RAAwB;oBACvB,WAAW,IAAA,4HAAE,EACX,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,4TAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,4TAAC,2RAAqB;QACpB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,KAIgC;QAJhC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C,GAJhC;IAKlB,qBACE,4TAAC,0RAAoB;QACnB,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,6aACA;QAED,GAAG,KAAK;;0BAET,4TAAC;gBAAK,WAAU;0BACd,cAAA,4TAAC,mSAA6B;8BAC5B,cAAA,4TAAC,4SAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,4TAAC,8RAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,SAAS,EACT,GAAG,OACoD,GAHhC;IAIvB,qBACE,4TAAC,+RAAyB;QACxB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,4TAAC,oSAA8B;QAC7B,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,4TAAC,4TAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,4TAAC,sSAAgC;QAC/B,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,4TAAC,kUAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 991, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,IAAA,qPAAG,EACvB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,gTAAI,GAAG;IAE9B,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 1044, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/lib/api.ts"], "sourcesContent": ["// Configuration de l'API\nexport const API_BASE_URL =\n\tprocess.env.NEXT_PUBLIC_API_URL || \"http://localhost:4000\";\n\n// Types pour l'authentification\nexport interface LoginRequest {\n\tusername: string;\n\tpassword: string;\n}\n\nexport interface LoginResponse {\n\taccess_token: string;\n\tuser: {\n\t\tid: string;\n\t\tusername: string;\n\t\tnom: string;\n\t\tprenom: string;\n\t\temail: string;\n\t\trole: \"admin\" | \"tresorier\" | \"membre\";\n\t\tstatut: \"actif\" | \"en_attente\" | \"suspendu\";\n\t};\n}\n\nexport interface RegisterRequest {\n\tusername: string;\n\tpassword: string;\n\tnom: string;\n\tprenom: string;\n\temail: string;\n\ttelephone: string;\n\trole: \"admin\" | \"tresorier\" | \"membre\";\n\tstatut?: \"actif\" | \"en_attente\" | \"suspendu\";\n}\n\nexport interface ApiError {\n\tmessage: string;\n\tstatusCode: number;\n\terror?: string;\n}\n\n// Service API\nexport class ApiService {\n\tprivate baseUrl: string;\n\n\tconstructor(baseUrl: string = API_BASE_URL) {\n\t\tthis.baseUrl = baseUrl;\n\t}\n\n\tprivate async request<T>(\n\t\tendpoint: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> {\n\t\tconst url = `${this.baseUrl}${endpoint}`;\n\n\t\tconst config: RequestInit = {\n\t\t\theaders: {\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\t...options.headers,\n\t\t\t},\n\t\t\t...options,\n\t\t};\n\n\t\ttry {\n\t\t\tconst response = await fetch(url, config);\n\n\t\t\tif (!response.ok) {\n\t\t\t\tconst errorData: ApiError = await response.json().catch(() => ({\n\t\t\t\t\tmessage: \"Une erreur est survenue\",\n\t\t\t\t\tstatusCode: response.status,\n\t\t\t\t}));\n\t\t\t\tthrow new Error(errorData.message || `HTTP ${response.status}`);\n\t\t\t}\n\n\t\t\treturn await response.json();\n\t\t} catch (error) {\n\t\t\tconsole.log(error);\n\n\t\t\tif (error instanceof Error) {\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t\tthrow new Error(\"Erreur de connexion au serveur\");\n\t\t}\n\t}\n\n\t// Authentification\n\tasync login(credentials: LoginRequest): Promise<LoginResponse> {\n\t\treturn this.request<LoginResponse>(\"/auth/login\", {\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(credentials),\n\t\t});\n\t}\n\n\tasync register(\n\t\tuserData: RegisterRequest,\n\t): Promise<{ message: string; user: any }> {\n\t\treturn this.request<{ message: string; user: any }>(\"/auth/register\", {\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(userData),\n\t\t});\n\t}\n\n\t// Méthodes avec authentification\n\tasync authenticatedRequest<T>(\n\t\tendpoint: string,\n\t\ttoken: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> {\n\t\treturn this.request<T>(endpoint, {\n\t\t\t...options,\n\t\t\theaders: {\n\t\t\t\t...options.headers,\n\t\t\t\tAuthorization: `Bearer ${token}`,\n\t\t\t},\n\t\t});\n\t}\n\n\t// Utilisateurs\n\tasync getUsers(token: string): Promise<any[]> {\n\t\treturn this.authenticatedRequest<any[]>(\"/users\", token);\n\t}\n\n\tasync getUser(id: string, token: string): Promise<any> {\n\t\treturn this.authenticatedRequest<any>(`/users/${id}`, token);\n\t}\n}\n\n// Instance par défaut\nexport const apiService = new ApiService();\n"], "names": [], "mappings": "AAAA,yBAAyB;;;;;;;;;AAExB;;;AADM,MAAM,eACZ,0SAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AAuC7B,MAAM;IAOZ,MAAc,QACb,QAAgB,EAEH;YADb,UAAA,iEAAuB,CAAC;QAExB,MAAM,MAAM,AAAC,GAAiB,OAAf,IAAI,CAAC,OAAO,EAAY,OAAT;QAE9B,MAAM,SAAsB;YAC3B,SAAS;gBACR,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACnB;YACA,GAAG,OAAO;QACX;QAEA,IAAI;YACH,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBACjB,MAAM,YAAsB,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;wBAC9D,SAAS;wBACT,YAAY,SAAS,MAAM;oBAC5B,CAAC;gBACD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,AAAC,QAAuB,OAAhB,SAAS,MAAM;YAC7D;YAEA,OAAO,MAAM,SAAS,IAAI;QAC3B,EAAE,OAAO,OAAO;YACf,QAAQ,GAAG,CAAC;YAEZ,IAAI,iBAAiB,OAAO;gBAC3B,MAAM;YACP;YACA,MAAM,IAAI,MAAM;QACjB;IACD;IAEA,mBAAmB;IACnB,MAAM,MAAM,WAAyB,EAA0B;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAgB,eAAe;YACjD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACtB;IACD;IAEA,MAAM,SACL,QAAyB,EACiB;QAC1C,OAAO,IAAI,CAAC,OAAO,CAAiC,kBAAkB;YACrE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACtB;IACD;IAEA,iCAAiC;IACjC,MAAM,qBACL,QAAgB,EAChB,KAAa,EAEA;YADb,UAAA,iEAAuB,CAAC;QAExB,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAChC,GAAG,OAAO;YACV,SAAS;gBACR,GAAG,QAAQ,OAAO;gBAClB,eAAe,AAAC,UAAe,OAAN;YAC1B;QACD;IACD;IAEA,eAAe;IACf,MAAM,SAAS,KAAa,EAAkB;QAC7C,OAAO,IAAI,CAAC,oBAAoB,CAAQ,UAAU;IACnD;IAEA,MAAM,QAAQ,EAAU,EAAE,KAAa,EAAgB;QACtD,OAAO,IAAI,CAAC,oBAAoB,CAAM,AAAC,UAAY,OAAH,KAAM;IACvD;IA/EA,YAAY,UAAkB,YAAY,CAAE;QAF5C,6OAAQ,WAAR,KAAA;QAGC,IAAI,CAAC,OAAO,GAAG;IAChB;AA8ED;AAGO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 1130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/hooks/use-api.ts"], "sourcesContent": ["import { useSession } from 'next-auth/react';\nimport { apiService } from '@/lib/api';\n\nexport function useApi() {\n  const { data: session } = useSession();\n\n  const authenticatedRequest = async <T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<T> => {\n    if (!session?.accessToken) {\n      throw new Error('Non authentifié');\n    }\n\n    return apiService.authenticatedRequest<T>(\n      endpoint,\n      session.accessToken,\n      options\n    );\n  };\n\n  return {\n    // Méthodes d'authentification (pas besoin de token)\n    login: apiService.login.bind(apiService),\n    register: apiService.register.bind(apiService),\n    \n    // Méthodes authentifiées\n    authenticatedRequest,\n    \n    // Raccourcis pour les endpoints courants\n    getUsers: () => authenticatedRequest<any[]>('/users'),\n    getUser: (id: string) => authenticatedRequest<any>(`/users/${id}`),\n    createUser: (userData: any) => authenticatedRequest<any>('/users', {\n      method: 'POST',\n      body: JSON.stringify(userData),\n    }),\n    updateUser: (id: string, userData: any) => authenticatedRequest<any>(`/users/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(userData),\n    }),\n    deleteUser: (id: string) => authenticatedRequest<any>(`/users/${id}`, {\n      method: 'DELETE',\n    }),\n    \n    // Sessions/Exercices\n    getSessions: () => authenticatedRequest<any[]>('/sessions'),\n    getSession: (id: string) => authenticatedRequest<any>(`/sessions/${id}`),\n    createSession: (sessionData: any) => authenticatedRequest<any>('/sessions', {\n      method: 'POST',\n      body: JSON.stringify(sessionData),\n    }),\n    \n    // Caisses\n    getCaisses: () => authenticatedRequest<any[]>('/caisses'),\n    getCaisse: (id: string) => authenticatedRequest<any>(`/caisses/${id}`),\n    createCaisse: (caisseData: any) => authenticatedRequest<any>('/caisses', {\n      method: 'POST',\n      body: JSON.stringify(caisseData),\n    }),\n    \n    // Réunions\n    getReunions: () => authenticatedRequest<any[]>('/reunions'),\n    getReunion: (id: string) => authenticatedRequest<any>(`/reunions/${id}`),\n  };\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEO,SAAS;;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,kQAAU;IAEpC,MAAM,uBAAuB,eAC3B;YACA,2EAAuB,CAAC;QAExB,IAAI,EAAC,oBAAA,8BAAA,QAAS,WAAW,GAAE;YACzB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,kIAAU,CAAC,oBAAoB,CACpC,UACA,QAAQ,WAAW,EACnB;IAEJ;IAEA,OAAO;QACL,oDAAoD;QACpD,OAAO,kIAAU,CAAC,KAAK,CAAC,IAAI,CAAC,kIAAU;QACvC,UAAU,kIAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,kIAAU;QAE7C,yBAAyB;QACzB;QAEA,yCAAyC;QACzC,UAAU,IAAM,qBAA4B;QAC5C,SAAS,CAAC,KAAe,qBAA0B,AAAC,UAAY,OAAH;QAC7D,YAAY,CAAC,WAAkB,qBAA0B,UAAU;gBACjE,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACvB;QACA,YAAY,CAAC,IAAY,WAAkB,qBAA0B,AAAC,UAAY,OAAH,KAAM;gBACnF,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACvB;QACA,YAAY,CAAC,KAAe,qBAA0B,AAAC,UAAY,OAAH,KAAM;gBACpE,QAAQ;YACV;QAEA,qBAAqB;QACrB,aAAa,IAAM,qBAA4B;QAC/C,YAAY,CAAC,KAAe,qBAA0B,AAAC,aAAe,OAAH;QACnE,eAAe,CAAC,cAAqB,qBAA0B,aAAa;gBAC1E,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACvB;QAEA,UAAU;QACV,YAAY,IAAM,qBAA4B;QAC9C,WAAW,CAAC,KAAe,qBAA0B,AAAC,YAAc,OAAH;QACjE,cAAc,CAAC,aAAoB,qBAA0B,YAAY;gBACvE,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACvB;QAEA,WAAW;QACX,aAAa,IAAM,qBAA4B;QAC/C,YAAY,CAAC,KAAe,qBAA0B,AAAC,aAAe,OAAH;IACrE;AACF;GA7DgB;;QACY,kQAAU", "debugId": null}}, {"offset": {"line": 1200, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/app/members/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useSession } from \"next-auth/react\";\nimport { Plus, Search, Filter, MoreHorizontal, Edit, Trash2, <PERSON>r<PERSON><PERSON><PERSON>, UserX } from \"lucide-react\";\nimport Link from \"next/link\";\n\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport {\n\tCard,\n\tCardContent,\n\tCardDescription,\n\tCardHeader,\n\tCardTitle,\n} from \"@/components/ui/card\";\nimport {\n\tTable,\n\tTableBody,\n\tTableCell,\n\tTableHead,\n\tTableHeader,\n\tTableRow,\n} from \"@/components/ui/table\";\nimport {\n\tDropdownMenu,\n\tDropdownMenuContent,\n\tDropdownMenuItem,\n\tDropdownMenuLabel,\n\tDropdownMenuSeparator,\n\tDropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport {\n\tSelect,\n\tSelectContent,\n\tSelectItem,\n\tSelectTrigger,\n\tSelectValue,\n} from \"@/components/ui/select\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { useApi } from \"@/hooks/use-api\";\n\ninterface Member {\n\t_id: string;\n\tusername: string;\n\tnom: string;\n\tprenom: string;\n\temail: string;\n\ttelephone: string;\n\trole: \"admin\" | \"tresorier\" | \"membre\";\n\tstatut: \"actif\" | \"en_attente\" | \"suspendu\";\n}\n\ninterface MemberStats {\n\ttotal: number;\n\tbyRole: Record<string, number>;\n\tbyStatus: Record<string, number>;\n\tactiveByRole: Record<string, number>;\n}\n\nexport default function MembersPage() {\n\tconst { data: session } = useSession();\n\tconst api = useApi();\n\t\n\tconst [members, setMembers] = useState<Member[]>([]);\n\tconst [stats, setStats] = useState<MemberStats | null>(null);\n\tconst [loading, setLoading] = useState(true);\n\tconst [searchTerm, setSearchTerm] = useState(\"\");\n\tconst [roleFilter, setRoleFilter] = useState<string>(\"all\");\n\tconst [statusFilter, setStatusFilter] = useState<string>(\"all\");\n\n\t// Vérifier les permissions\n\tconst canManageMembers = session?.user && \n\t\t((session.user as any).role === \"admin\" || (session.user as any).role === \"tresorier\");\n\tconst canEditMembers = session?.user && (session.user as any).role === \"admin\";\n\n\tuseEffect(() => {\n\t\tif (session?.accessToken) {\n\t\t\tloadData();\n\t\t}\n\t}, [session, searchTerm, roleFilter, statusFilter]);\n\n\tconst loadData = async () => {\n\t\ttry {\n\t\t\tsetLoading(true);\n\t\t\t\n\t\t\t// Construire les paramètres de requête\n\t\t\tconst params = new URLSearchParams();\n\t\t\tif (roleFilter !== \"all\") params.append(\"role\", roleFilter);\n\t\t\tif (statusFilter !== \"all\") params.append(\"statut\", statusFilter);\n\t\t\tif (searchTerm) params.append(\"search\", searchTerm);\n\n\t\t\tconst queryString = params.toString();\n\t\t\tconst endpoint = `/users${queryString ? `?${queryString}` : \"\"}`;\n\n\t\t\t// Charger les membres et les statistiques en parallèle\n\t\t\tconst [membersData, statsData] = await Promise.all([\n\t\t\t\tapi.authenticatedRequest<Member[]>(endpoint),\n\t\t\t\tapi.authenticatedRequest<MemberStats>(\"/users/stats\"),\n\t\t\t]);\n\n\t\t\tsetMembers(membersData);\n\t\t\tsetStats(statsData);\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Erreur lors du chargement des données:\", error);\n\t\t} finally {\n\t\t\tsetLoading(false);\n\t\t}\n\t};\n\n\tconst handleStatusChange = async (memberId: string, newStatus: string) => {\n\t\ttry {\n\t\t\tawait api.authenticatedRequest(`/users/${memberId}/statut/${newStatus}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t});\n\t\t\t\n\t\t\t// Recharger les données\n\t\t\tloadData();\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Erreur lors du changement de statut:\", error);\n\t\t}\n\t};\n\n\tconst getStatusBadge = (status: string) => {\n\t\tconst variants = {\n\t\t\tactif: \"default\",\n\t\t\ten_attente: \"secondary\",\n\t\t\tsuspendu: \"destructive\",\n\t\t} as const;\n\n\t\treturn (\n\t\t\t<Badge variant={variants[status as keyof typeof variants] || \"secondary\"}>\n\t\t\t\t{status.replace(\"_\", \" \")}\n\t\t\t</Badge>\n\t\t);\n\t};\n\n\tconst getRoleBadge = (role: string) => {\n\t\tconst variants = {\n\t\t\tadmin: \"destructive\",\n\t\t\ttresorier: \"default\",\n\t\t\tmembre: \"secondary\",\n\t\t} as const;\n\n\t\treturn (\n\t\t\t<Badge variant={variants[role as keyof typeof variants] || \"secondary\"}>\n\t\t\t\t{role}\n\t\t\t</Badge>\n\t\t);\n\t};\n\n\tif (!canManageMembers) {\n\t\treturn (\n\t\t\t<div className=\"flex items-center justify-center h-64\">\n\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t<h2 className=\"text-lg font-semibold text-gray-900\">Accès refusé</h2>\n\t\t\t\t\t<p className=\"text-gray-600\">\n\t\t\t\t\t\tVous n'avez pas les permissions pour accéder à cette page.\n\t\t\t\t\t</p>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t);\n\t}\n\n\treturn (\n\t\t<div className=\"space-y-6\">\n\t\t\t{/* En-tête */}\n\t\t\t<div className=\"flex justify-between items-center\">\n\t\t\t\t<div>\n\t\t\t\t\t<h1 className=\"text-2xl font-bold text-gray-900\">Gestion des Membres</h1>\n\t\t\t\t\t<p className=\"text-gray-600\">\n\t\t\t\t\t\tGérez les membres de votre tontine\n\t\t\t\t\t</p>\n\t\t\t\t</div>\n\t\t\t\t{canEditMembers && (\n\t\t\t\t\t<Link href=\"/members/new\">\n\t\t\t\t\t\t<Button>\n\t\t\t\t\t\t\t<Plus className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\t\tNouveau membre\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</Link>\n\t\t\t\t)}\n\t\t\t</div>\n\n\t\t\t{/* Statistiques */}\n\t\t\t{stats && (\n\t\t\t\t<div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n\t\t\t\t\t<Card>\n\t\t\t\t\t\t<CardHeader className=\"pb-2\">\n\t\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium text-gray-600\">\n\t\t\t\t\t\t\t\tTotal Membres\n\t\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t\t</CardHeader>\n\t\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t\t<div className=\"text-2xl font-bold\">{stats.total}</div>\n\t\t\t\t\t\t</CardContent>\n\t\t\t\t\t</Card>\n\t\t\t\t\t<Card>\n\t\t\t\t\t\t<CardHeader className=\"pb-2\">\n\t\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium text-gray-600\">\n\t\t\t\t\t\t\t\tMembres Actifs\n\t\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t\t</CardHeader>\n\t\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t\t<div className=\"text-2xl font-bold text-green-600\">\n\t\t\t\t\t\t\t\t{stats.byStatus.actif || 0}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</CardContent>\n\t\t\t\t\t</Card>\n\t\t\t\t\t<Card>\n\t\t\t\t\t\t<CardHeader className=\"pb-2\">\n\t\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium text-gray-600\">\n\t\t\t\t\t\t\t\tEn Attente\n\t\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t\t</CardHeader>\n\t\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t\t<div className=\"text-2xl font-bold text-orange-600\">\n\t\t\t\t\t\t\t\t{stats.byStatus.en_attente || 0}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</CardContent>\n\t\t\t\t\t</Card>\n\t\t\t\t\t<Card>\n\t\t\t\t\t\t<CardHeader className=\"pb-2\">\n\t\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium text-gray-600\">\n\t\t\t\t\t\t\t\tSuspendus\n\t\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t\t</CardHeader>\n\t\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t\t<div className=\"text-2xl font-bold text-red-600\">\n\t\t\t\t\t\t\t\t{stats.byStatus.suspendu || 0}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</CardContent>\n\t\t\t\t\t</Card>\n\t\t\t\t</div>\n\t\t\t)}\n\n\t\t\t{/* Filtres et recherche */}\n\t\t\t<Card>\n\t\t\t\t<CardHeader>\n\t\t\t\t\t<CardTitle>Filtres</CardTitle>\n\t\t\t\t</CardHeader>\n\t\t\t\t<CardContent>\n\t\t\t\t\t<div className=\"flex flex-col sm:flex-row gap-4\">\n\t\t\t\t\t\t<div className=\"flex-1\">\n\t\t\t\t\t\t\t<div className=\"relative\">\n\t\t\t\t\t\t\t\t<Search className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n\t\t\t\t\t\t\t\t<Input\n\t\t\t\t\t\t\t\t\tplaceholder=\"Rechercher par nom, prénom, email...\"\n\t\t\t\t\t\t\t\t\tvalue={searchTerm}\n\t\t\t\t\t\t\t\t\tonChange={(e) => setSearchTerm(e.target.value)}\n\t\t\t\t\t\t\t\t\tclassName=\"pl-10\"\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<Select value={roleFilter} onValueChange={setRoleFilter}>\n\t\t\t\t\t\t\t<SelectTrigger className=\"w-full sm:w-48\">\n\t\t\t\t\t\t\t\t<SelectValue placeholder=\"Filtrer par rôle\" />\n\t\t\t\t\t\t\t</SelectTrigger>\n\t\t\t\t\t\t\t<SelectContent>\n\t\t\t\t\t\t\t\t<SelectItem value=\"all\">Tous les rôles</SelectItem>\n\t\t\t\t\t\t\t\t<SelectItem value=\"admin\">Admin</SelectItem>\n\t\t\t\t\t\t\t\t<SelectItem value=\"tresorier\">Trésorier</SelectItem>\n\t\t\t\t\t\t\t\t<SelectItem value=\"membre\">Membre</SelectItem>\n\t\t\t\t\t\t\t</SelectContent>\n\t\t\t\t\t\t</Select>\n\t\t\t\t\t\t<Select value={statusFilter} onValueChange={setStatusFilter}>\n\t\t\t\t\t\t\t<SelectTrigger className=\"w-full sm:w-48\">\n\t\t\t\t\t\t\t\t<SelectValue placeholder=\"Filtrer par statut\" />\n\t\t\t\t\t\t\t</SelectTrigger>\n\t\t\t\t\t\t\t<SelectContent>\n\t\t\t\t\t\t\t\t<SelectItem value=\"all\">Tous les statuts</SelectItem>\n\t\t\t\t\t\t\t\t<SelectItem value=\"actif\">Actif</SelectItem>\n\t\t\t\t\t\t\t\t<SelectItem value=\"en_attente\">En attente</SelectItem>\n\t\t\t\t\t\t\t\t<SelectItem value=\"suspendu\">Suspendu</SelectItem>\n\t\t\t\t\t\t\t</SelectContent>\n\t\t\t\t\t\t</Select>\n\t\t\t\t\t</div>\n\t\t\t\t</CardContent>\n\t\t\t</Card>\n\n\t\t\t{/* Liste des membres */}\n\t\t\t<Card>\n\t\t\t\t<CardHeader>\n\t\t\t\t\t<CardTitle>Liste des Membres</CardTitle>\n\t\t\t\t\t<CardDescription>\n\t\t\t\t\t\t{members.length} membre(s) trouvé(s)\n\t\t\t\t\t</CardDescription>\n\t\t\t\t</CardHeader>\n\t\t\t\t<CardContent>\n\t\t\t\t\t{loading ? (\n\t\t\t\t\t\t<div className=\"flex justify-center py-8\">\n\t\t\t\t\t\t\t<div className=\"text-gray-500\">Chargement...</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t) : (\n\t\t\t\t\t\t<Table>\n\t\t\t\t\t\t\t<TableHeader>\n\t\t\t\t\t\t\t\t<TableRow>\n\t\t\t\t\t\t\t\t\t<TableHead>Nom</TableHead>\n\t\t\t\t\t\t\t\t\t<TableHead>Email</TableHead>\n\t\t\t\t\t\t\t\t\t<TableHead>Téléphone</TableHead>\n\t\t\t\t\t\t\t\t\t<TableHead>Rôle</TableHead>\n\t\t\t\t\t\t\t\t\t<TableHead>Statut</TableHead>\n\t\t\t\t\t\t\t\t\t{canEditMembers && <TableHead>Actions</TableHead>}\n\t\t\t\t\t\t\t\t</TableRow>\n\t\t\t\t\t\t\t</TableHeader>\n\t\t\t\t\t\t\t<TableBody>\n\t\t\t\t\t\t\t\t{members.map((member) => (\n\t\t\t\t\t\t\t\t\t<TableRow key={member._id}>\n\t\t\t\t\t\t\t\t\t\t<TableCell>\n\t\t\t\t\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t\t\t\t\t<div className=\"font-medium\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t{member.prenom} {member.nom}\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t<div className=\"text-sm text-gray-500\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t@{member.username}\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t</TableCell>\n\t\t\t\t\t\t\t\t\t\t<TableCell>{member.email}</TableCell>\n\t\t\t\t\t\t\t\t\t\t<TableCell>{member.telephone}</TableCell>\n\t\t\t\t\t\t\t\t\t\t<TableCell>{getRoleBadge(member.role)}</TableCell>\n\t\t\t\t\t\t\t\t\t\t<TableCell>{getStatusBadge(member.statut)}</TableCell>\n\t\t\t\t\t\t\t\t\t\t{canEditMembers && (\n\t\t\t\t\t\t\t\t\t\t\t<TableCell>\n\t\t\t\t\t\t\t\t\t\t\t\t<DropdownMenu>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<DropdownMenuTrigger asChild>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Button variant=\"ghost\" className=\"h-8 w-8 p-0\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<MoreHorizontal className=\"h-4 w-4\" />\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</DropdownMenuTrigger>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<DropdownMenuContent align=\"end\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<DropdownMenuLabel>Actions</DropdownMenuLabel>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<DropdownMenuItem asChild>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Link href={`/members/${member._id}/edit`}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Edit className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tModifier\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Link>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</DropdownMenuItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<DropdownMenuSeparator />\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{member.statut === \"actif\" ? (\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<DropdownMenuItem\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleStatusChange(member._id, \"suspendu\")}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<UserX className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tSuspendre\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</DropdownMenuItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t) : (\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<DropdownMenuItem\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleStatusChange(member._id, \"actif\")}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<UserCheck className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tActiver\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</DropdownMenuItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t\t\t\t\t</DropdownMenuContent>\n\t\t\t\t\t\t\t\t\t\t\t\t</DropdownMenu>\n\t\t\t\t\t\t\t\t\t\t\t</TableCell>\n\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t</TableRow>\n\t\t\t\t\t\t\t\t))}\n\t\t\t\t\t\t\t</TableBody>\n\t\t\t\t\t\t</Table>\n\t\t\t\t\t)}\n\t\t\t\t</CardContent>\n\t\t\t</Card>\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAOA;AAQA;AAQA;AAOA;AACA;;;AAxCA;;;;;;;;;;;;;AA4De,SAAS;;IACvB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,kQAAU;IACpC,MAAM,MAAM,IAAA,uIAAM;IAElB,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,wSAAQ,EAAW,EAAE;IACnD,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,wSAAQ,EAAqB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,wSAAQ,EAAC;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,wSAAQ,EAAC;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,wSAAQ,EAAS;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,wSAAQ,EAAS;IAEzD,2BAA2B;IAC3B,MAAM,mBAAmB,CAAA,oBAAA,8BAAA,QAAS,IAAI,KACrC,CAAC,AAAC,QAAQ,IAAI,CAAS,IAAI,KAAK,WAAW,AAAC,QAAQ,IAAI,CAAS,IAAI,KAAK,WAAW;IACtF,MAAM,iBAAiB,CAAA,oBAAA,8BAAA,QAAS,IAAI,KAAI,AAAC,QAAQ,IAAI,CAAS,IAAI,KAAK;IAEvE,IAAA,ySAAS;iCAAC;YACT,IAAI,oBAAA,8BAAA,QAAS,WAAW,EAAE;gBACzB;YACD;QACD;gCAAG;QAAC;QAAS;QAAY;QAAY;KAAa;IAElD,MAAM,WAAW;QAChB,IAAI;YACH,WAAW;YAEX,uCAAuC;YACvC,MAAM,SAAS,IAAI;YACnB,IAAI,eAAe,OAAO,OAAO,MAAM,CAAC,QAAQ;YAChD,IAAI,iBAAiB,OAAO,OAAO,MAAM,CAAC,UAAU;YACpD,IAAI,YAAY,OAAO,MAAM,CAAC,UAAU;YAExC,MAAM,cAAc,OAAO,QAAQ;YACnC,MAAM,WAAW,AAAC,SAA6C,OAArC,cAAc,AAAC,IAAe,OAAZ,eAAgB;YAE5D,uDAAuD;YACvD,MAAM,CAAC,aAAa,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAClD,IAAI,oBAAoB,CAAW;gBACnC,IAAI,oBAAoB,CAAc;aACtC;YAED,WAAW;YACX,SAAS;QACV,EAAE,OAAO,OAAO;YACf,QAAQ,KAAK,CAAC,0CAA0C;QACzD,SAAU;YACT,WAAW;QACZ;IACD;IAEA,MAAM,qBAAqB,OAAO,UAAkB;QACnD,IAAI;YACH,MAAM,IAAI,oBAAoB,CAAC,AAAC,UAA4B,OAAnB,UAAS,YAAoB,OAAV,YAAa;gBACxE,QAAQ;YACT;YAEA,wBAAwB;YACxB;QACD,EAAE,OAAO,OAAO;YACf,QAAQ,KAAK,CAAC,wCAAwC;QACvD;IACD;IAEA,MAAM,iBAAiB,CAAC;QACvB,MAAM,WAAW;YAChB,OAAO;YACP,YAAY;YACZ,UAAU;QACX;QAEA,qBACC,4TAAC,6IAAK;YAAC,SAAS,QAAQ,CAAC,OAAgC,IAAI;sBAC3D,OAAO,OAAO,CAAC,KAAK;;;;;;IAGxB;IAEA,MAAM,eAAe,CAAC;QACrB,MAAM,WAAW;YAChB,OAAO;YACP,WAAW;YACX,QAAQ;QACT;QAEA,qBACC,4TAAC,6IAAK;YAAC,SAAS,QAAQ,CAAC,KAA8B,IAAI;sBACzD;;;;;;IAGJ;IAEA,IAAI,CAAC,kBAAkB;QACtB,qBACC,4TAAC;YAAI,WAAU;sBACd,cAAA,4TAAC;gBAAI,WAAU;;kCACd,4TAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,4TAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAMjC;IAEA,qBACC,4TAAC;QAAI,WAAU;;0BAEd,4TAAC;gBAAI,WAAU;;kCACd,4TAAC;;0CACA,4TAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,4TAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;oBAI7B,gCACA,4TAAC,ySAAI;wBAAC,MAAK;kCACV,cAAA,4TAAC,+IAAM;;8CACN,4TAAC,iSAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;YAQpC,uBACA,4TAAC;gBAAI,WAAU;;kCACd,4TAAC,2IAAI;;0CACJ,4TAAC,iJAAU;gCAAC,WAAU;0CACrB,cAAA,4TAAC,gJAAS;oCAAC,WAAU;8CAAoC;;;;;;;;;;;0CAI1D,4TAAC,kJAAW;0CACX,cAAA,4TAAC;oCAAI,WAAU;8CAAsB,MAAM,KAAK;;;;;;;;;;;;;;;;;kCAGlD,4TAAC,2IAAI;;0CACJ,4TAAC,iJAAU;gCAAC,WAAU;0CACrB,cAAA,4TAAC,gJAAS;oCAAC,WAAU;8CAAoC;;;;;;;;;;;0CAI1D,4TAAC,kJAAW;0CACX,cAAA,4TAAC;oCAAI,WAAU;8CACb,MAAM,QAAQ,CAAC,KAAK,IAAI;;;;;;;;;;;;;;;;;kCAI5B,4TAAC,2IAAI;;0CACJ,4TAAC,iJAAU;gCAAC,WAAU;0CACrB,cAAA,4TAAC,gJAAS;oCAAC,WAAU;8CAAoC;;;;;;;;;;;0CAI1D,4TAAC,kJAAW;0CACX,cAAA,4TAAC;oCAAI,WAAU;8CACb,MAAM,QAAQ,CAAC,UAAU,IAAI;;;;;;;;;;;;;;;;;kCAIjC,4TAAC,2IAAI;;0CACJ,4TAAC,iJAAU;gCAAC,WAAU;0CACrB,cAAA,4TAAC,gJAAS;oCAAC,WAAU;8CAAoC;;;;;;;;;;;0CAI1D,4TAAC,kJAAW;0CACX,cAAA,4TAAC;oCAAI,WAAU;8CACb,MAAM,QAAQ,CAAC,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;0BAQjC,4TAAC,2IAAI;;kCACJ,4TAAC,iJAAU;kCACV,cAAA,4TAAC,gJAAS;sCAAC;;;;;;;;;;;kCAEZ,4TAAC,kJAAW;kCACX,cAAA,4TAAC;4BAAI,WAAU;;8CACd,4TAAC;oCAAI,WAAU;8CACd,cAAA,4TAAC;wCAAI,WAAU;;0DACd,4TAAC,uSAAM;gDAAC,WAAU;;;;;;0DAClB,4TAAC,6IAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;;;;;;8CAIb,4TAAC,+IAAM;oCAAC,OAAO;oCAAY,eAAe;;sDACzC,4TAAC,sJAAa;4CAAC,WAAU;sDACxB,cAAA,4TAAC,oJAAW;gDAAC,aAAY;;;;;;;;;;;sDAE1B,4TAAC,sJAAa;;8DACb,4TAAC,mJAAU;oDAAC,OAAM;8DAAM;;;;;;8DACxB,4TAAC,mJAAU;oDAAC,OAAM;8DAAQ;;;;;;8DAC1B,4TAAC,mJAAU;oDAAC,OAAM;8DAAY;;;;;;8DAC9B,4TAAC,mJAAU;oDAAC,OAAM;8DAAS;;;;;;;;;;;;;;;;;;8CAG7B,4TAAC,+IAAM;oCAAC,OAAO;oCAAc,eAAe;;sDAC3C,4TAAC,sJAAa;4CAAC,WAAU;sDACxB,cAAA,4TAAC,oJAAW;gDAAC,aAAY;;;;;;;;;;;sDAE1B,4TAAC,sJAAa;;8DACb,4TAAC,mJAAU;oDAAC,OAAM;8DAAM;;;;;;8DACxB,4TAAC,mJAAU;oDAAC,OAAM;8DAAQ;;;;;;8DAC1B,4TAAC,mJAAU;oDAAC,OAAM;8DAAa;;;;;;8DAC/B,4TAAC,mJAAU;oDAAC,OAAM;8DAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlC,4TAAC,2IAAI;;kCACJ,4TAAC,iJAAU;;0CACV,4TAAC,gJAAS;0CAAC;;;;;;0CACX,4TAAC,sJAAe;;oCACd,QAAQ,MAAM;oCAAC;;;;;;;;;;;;;kCAGlB,4TAAC,kJAAW;kCACV,wBACA,4TAAC;4BAAI,WAAU;sCACd,cAAA,4TAAC;gCAAI,WAAU;0CAAgB;;;;;;;;;;iDAGhC,4TAAC,6IAAK;;8CACL,4TAAC,mJAAW;8CACX,cAAA,4TAAC,gJAAQ;;0DACR,4TAAC,iJAAS;0DAAC;;;;;;0DACX,4TAAC,iJAAS;0DAAC;;;;;;0DACX,4TAAC,iJAAS;0DAAC;;;;;;0DACX,4TAAC,iJAAS;0DAAC;;;;;;0DACX,4TAAC,iJAAS;0DAAC;;;;;;4CACV,gCAAkB,4TAAC,iJAAS;0DAAC;;;;;;;;;;;;;;;;;8CAGhC,4TAAC,iJAAS;8CACR,QAAQ,GAAG,CAAC,CAAC,uBACb,4TAAC,gJAAQ;;8DACR,4TAAC,iJAAS;8DACT,cAAA,4TAAC;;0EACA,4TAAC;gEAAI,WAAU;;oEACb,OAAO,MAAM;oEAAC;oEAAE,OAAO,GAAG;;;;;;;0EAE5B,4TAAC;gEAAI,WAAU;;oEAAwB;oEACpC,OAAO,QAAQ;;;;;;;;;;;;;;;;;;8DAIpB,4TAAC,iJAAS;8DAAE,OAAO,KAAK;;;;;;8DACxB,4TAAC,iJAAS;8DAAE,OAAO,SAAS;;;;;;8DAC5B,4TAAC,iJAAS;8DAAE,aAAa,OAAO,IAAI;;;;;;8DACpC,4TAAC,iJAAS;8DAAE,eAAe,OAAO,MAAM;;;;;;gDACvC,gCACA,4TAAC,iJAAS;8DACT,cAAA,4TAAC,+JAAY;;0EACZ,4TAAC,sKAAmB;gEAAC,OAAO;0EAC3B,cAAA,4TAAC,+IAAM;oEAAC,SAAQ;oEAAQ,WAAU;8EACjC,cAAA,4TAAC,yTAAc;wEAAC,WAAU;;;;;;;;;;;;;;;;0EAG5B,4TAAC,sKAAmB;gEAAC,OAAM;;kFAC1B,4TAAC,oKAAiB;kFAAC;;;;;;kFACnB,4TAAC,mKAAgB;wEAAC,OAAO;kFACxB,cAAA,4TAAC,ySAAI;4EAAC,MAAM,AAAC,YAAsB,OAAX,OAAO,GAAG,EAAC;;8FAClC,4TAAC,0SAAI;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;;;;;;kFAInC,4TAAC,wKAAqB;;;;;oEACrB,OAAO,MAAM,KAAK,wBAClB,4TAAC,mKAAgB;wEAChB,SAAS,IAAM,mBAAmB,OAAO,GAAG,EAAE;;0FAE9C,4TAAC,wSAAK;gFAAC,WAAU;;;;;;4EAAiB;;;;;;6FAInC,4TAAC,mKAAgB;wEAChB,SAAS,IAAM,mBAAmB,OAAO,GAAG,EAAE;;0FAE9C,4TAAC,oTAAS;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;;;;;;;;2CA3C9B,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DlC;GAnTwB;;QACG,kQAAU;QACxB,uIAAM;;;KAFK", "debugId": null}}]}