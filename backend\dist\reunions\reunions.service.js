"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReunionsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const reunion_schema_1 = require("./schemas/reunion.schema");
let ReunionsService = class ReunionsService {
    constructor(reunionModel) {
        this.reunionModel = reunionModel;
    }
    async findAll() {
        return this.reunionModel.find().sort({ dateReunion: 1 }).lean();
    }
    async findOne(id) {
        if (!mongoose_2.Types.ObjectId.isValid(id))
            throw new common_1.NotFoundException('Réunion introuvable');
        return this.reunionModel.findById(id).lean();
    }
    async update(id, dto) {
        if (!mongoose_2.Types.ObjectId.isValid(id))
            throw new common_1.NotFoundException('Réunion introuvable');
        // Prevent setting a past date if needed; here just transform
        const update = {};
        if (dto.dateReunion)
            update.dateReunion = new Date(dto.dateReunion);
        if (dto.lieu !== undefined)
            update.lieu = dto.lieu;
        if (dto.caissePrincipale)
            update.caissePrincipale = new mongoose_2.Types.ObjectId(dto.caissePrincipale);
        const updated = await this.reunionModel.findByIdAndUpdate(id, { $set: update }, { new: true });
        return updated?.toObject() ?? null;
    }
};
exports.ReunionsService = ReunionsService;
exports.ReunionsService = ReunionsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(reunion_schema_1.Reunion.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], ReunionsService);
