"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReunionsModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const reunions_controller_1 = require("./reunions.controller");
const reunions_service_1 = require("./reunions.service");
const reunion_schema_1 = require("./schemas/reunion.schema");
let ReunionsModule = class ReunionsModule {
};
exports.ReunionsModule = ReunionsModule;
exports.ReunionsModule = ReunionsModule = __decorate([
    (0, common_1.Module)({
        imports: [mongoose_1.MongooseModule.forFeature([{ name: reunion_schema_1.Reunion.name, schema: reunion_schema_1.ReunionSchema }])],
        controllers: [reunions_controller_1.ReunionsController],
        providers: [reunions_service_1.ReunionsService],
        exports: [reunions_service_1.ReunionsService],
    })
], ReunionsModule);
