import { Injectable } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { UsersService } from "../users/users.service";
import { User, UserDocument } from "../users/schemas/user.schema";
import { RegisterDto } from "./dto/register.dto";

@Injectable()
export class AuthService {
	constructor(
		private usersService: UsersService,
		private jwtService: JwtService,
	) {}

	async validateUser(username: string, pass: string): Promise<User | null> {
		return this.usersService.validateUser(username, pass);
	}

	async login(user: User) {
		const id = await this.usersService.getUserId(user.username);
		const payload = { username: user.username, sub: id, role: user.role };
		return {
			access_token: this.jwtService.sign(payload),
			user: {
				id: id,
				username: user.username,
				nom: user.nom,
				prenom: user.prenom,
				email: user.email,
				role: user.role,
				statut: user.statut,
			},
		};
	}

	async register(registerDto: RegisterDto): Promise<any> {
		const user = await this.usersService.create(registerDto);
		// Convertir le document Mongoose en objet plain pour accéder à _id
		const userObj = user.toObject();
		return {
			_id: userObj._id,
			username: userObj.username,
			nom: userObj.nom,
			prenom: userObj.prenom,
			email: userObj.email,
			role: userObj.role,
			statut: userObj.statut,
		};
	}
}
