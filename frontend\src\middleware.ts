import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";

export default auth((req) => {
	const { nextUrl } = req;

	// Ex: protéger tout sauf /login et /public
	const isAuthPage =
		nextUrl.pathname.startsWith("/auth") || nextUrl.pathname === "/";

	if (!req.auth && !isAuthPage) {
		// pas connecté -> redirection login
		return NextResponse.redirect(new URL("/auth/signin", nextUrl));
	}

	// si déjà connecté et tente d'aller sur /login -> on redirige ailleurs
	if (
		(req.auth && nextUrl.pathname.startsWith("/auth/signin")) ||
		nextUrl.pathname === "/"
	) {
		return NextResponse.redirect(new URL("/dashboard", nextUrl));
	}

	return NextResponse.next();
});

// ⬇️ obligatoire : config matcher
export const config = {
	matcher: [
		"/((?!api|_next/static|_next/image|favicon.ico).*)", // toutes les routes sauf API/static
	],
};
