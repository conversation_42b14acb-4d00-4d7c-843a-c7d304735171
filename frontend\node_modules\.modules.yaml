hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': public
  '@auth/core@0.40.0':
    '@auth/core': public
  '@biomejs/cli-darwin-arm64@2.2.0':
    '@biomejs/cli-darwin-arm64': public
  '@biomejs/cli-darwin-x64@2.2.0':
    '@biomejs/cli-darwin-x64': public
  '@biomejs/cli-linux-arm64-musl@2.2.0':
    '@biomejs/cli-linux-arm64-musl': public
  '@biomejs/cli-linux-arm64@2.2.0':
    '@biomejs/cli-linux-arm64': public
  '@biomejs/cli-linux-x64-musl@2.2.0':
    '@biomejs/cli-linux-x64-musl': public
  '@biomejs/cli-linux-x64@2.2.0':
    '@biomejs/cli-linux-x64': public
  '@biomejs/cli-win32-arm64@2.2.0':
    '@biomejs/cli-win32-arm64': public
  '@biomejs/cli-win32-x64@2.2.0':
    '@biomejs/cli-win32-x64': public
  '@floating-ui/core@1.7.3':
    '@floating-ui/core': public
  '@floating-ui/dom@1.7.4':
    '@floating-ui/dom': public
  '@floating-ui/react-dom@2.1.6(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@floating-ui/react-dom': public
  '@floating-ui/utils@0.2.10':
    '@floating-ui/utils': public
  '@img/sharp-darwin-arm64@0.34.3':
    '@img/sharp-darwin-arm64': public
  '@img/sharp-darwin-x64@0.34.3':
    '@img/sharp-darwin-x64': public
  '@img/sharp-libvips-darwin-arm64@1.2.0':
    '@img/sharp-libvips-darwin-arm64': public
  '@img/sharp-libvips-darwin-x64@1.2.0':
    '@img/sharp-libvips-darwin-x64': public
  '@img/sharp-libvips-linux-arm64@1.2.0':
    '@img/sharp-libvips-linux-arm64': public
  '@img/sharp-libvips-linux-arm@1.2.0':
    '@img/sharp-libvips-linux-arm': public
  '@img/sharp-libvips-linux-ppc64@1.2.0':
    '@img/sharp-libvips-linux-ppc64': public
  '@img/sharp-libvips-linux-s390x@1.2.0':
    '@img/sharp-libvips-linux-s390x': public
  '@img/sharp-libvips-linux-x64@1.2.0':
    '@img/sharp-libvips-linux-x64': public
  '@img/sharp-libvips-linuxmusl-arm64@1.2.0':
    '@img/sharp-libvips-linuxmusl-arm64': public
  '@img/sharp-libvips-linuxmusl-x64@1.2.0':
    '@img/sharp-libvips-linuxmusl-x64': public
  '@img/sharp-linux-arm64@0.34.3':
    '@img/sharp-linux-arm64': public
  '@img/sharp-linux-arm@0.34.3':
    '@img/sharp-linux-arm': public
  '@img/sharp-linux-ppc64@0.34.3':
    '@img/sharp-linux-ppc64': public
  '@img/sharp-linux-s390x@0.34.3':
    '@img/sharp-linux-s390x': public
  '@img/sharp-linux-x64@0.34.3':
    '@img/sharp-linux-x64': public
  '@img/sharp-linuxmusl-arm64@0.34.3':
    '@img/sharp-linuxmusl-arm64': public
  '@img/sharp-linuxmusl-x64@0.34.3':
    '@img/sharp-linuxmusl-x64': public
  '@img/sharp-wasm32@0.34.3':
    '@img/sharp-wasm32': public
  '@img/sharp-win32-arm64@0.34.3':
    '@img/sharp-win32-arm64': public
  '@img/sharp-win32-ia32@0.34.3':
    '@img/sharp-win32-ia32': public
  '@img/sharp-win32-x64@0.34.3':
    '@img/sharp-win32-x64': public
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': public
  '@jridgewell/gen-mapping@0.3.13':
    '@jridgewell/gen-mapping': public
  '@jridgewell/remapping@2.3.5':
    '@jridgewell/remapping': public
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': public
  '@jridgewell/sourcemap-codec@1.5.5':
    '@jridgewell/sourcemap-codec': public
  '@jridgewell/trace-mapping@0.3.30':
    '@jridgewell/trace-mapping': public
  '@next/env@15.5.2':
    '@next/env': public
  '@next/swc-darwin-arm64@15.5.2':
    '@next/swc-darwin-arm64': public
  '@next/swc-darwin-x64@15.5.2':
    '@next/swc-darwin-x64': public
  '@next/swc-linux-arm64-gnu@15.5.2':
    '@next/swc-linux-arm64-gnu': public
  '@next/swc-linux-arm64-musl@15.5.2':
    '@next/swc-linux-arm64-musl': public
  '@next/swc-linux-x64-gnu@15.5.2':
    '@next/swc-linux-x64-gnu': public
  '@next/swc-linux-x64-musl@15.5.2':
    '@next/swc-linux-x64-musl': public
  '@next/swc-win32-arm64-msvc@15.5.2':
    '@next/swc-win32-arm64-msvc': public
  '@next/swc-win32-x64-msvc@15.5.2':
    '@next/swc-win32-x64-msvc': public
  '@panva/hkdf@1.2.1':
    '@panva/hkdf': public
  '@radix-ui/number@1.1.1':
    '@radix-ui/number': public
  '@radix-ui/primitive@1.1.3':
    '@radix-ui/primitive': public
  '@radix-ui/react-arrow@1.1.7(@types/react-dom@19.1.9(@types/react@19.1.12))(@types/react@19.1.12)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-arrow': public
  '@radix-ui/react-collection@1.1.7(@types/react-dom@19.1.9(@types/react@19.1.12))(@types/react@19.1.12)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-collection': public
  '@radix-ui/react-compose-refs@1.1.2(@types/react@19.1.12)(react@19.1.0)':
    '@radix-ui/react-compose-refs': public
  '@radix-ui/react-context@1.1.2(@types/react@19.1.12)(react@19.1.0)':
    '@radix-ui/react-context': public
  '@radix-ui/react-direction@1.1.1(@types/react@19.1.12)(react@19.1.0)':
    '@radix-ui/react-direction': public
  '@radix-ui/react-dismissable-layer@1.1.11(@types/react-dom@19.1.9(@types/react@19.1.12))(@types/react@19.1.12)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-dismissable-layer': public
  '@radix-ui/react-focus-guards@1.1.3(@types/react@19.1.12)(react@19.1.0)':
    '@radix-ui/react-focus-guards': public
  '@radix-ui/react-focus-scope@1.1.7(@types/react-dom@19.1.9(@types/react@19.1.12))(@types/react@19.1.12)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-focus-scope': public
  '@radix-ui/react-id@1.1.1(@types/react@19.1.12)(react@19.1.0)':
    '@radix-ui/react-id': public
  '@radix-ui/react-menu@2.1.16(@types/react-dom@19.1.9(@types/react@19.1.12))(@types/react@19.1.12)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-menu': public
  '@radix-ui/react-popper@1.2.8(@types/react-dom@19.1.9(@types/react@19.1.12))(@types/react@19.1.12)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-popper': public
  '@radix-ui/react-portal@1.1.9(@types/react-dom@19.1.9(@types/react@19.1.12))(@types/react@19.1.12)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-portal': public
  '@radix-ui/react-presence@1.1.5(@types/react-dom@19.1.9(@types/react@19.1.12))(@types/react@19.1.12)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-presence': public
  '@radix-ui/react-primitive@2.1.3(@types/react-dom@19.1.9(@types/react@19.1.12))(@types/react@19.1.12)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-primitive': public
  '@radix-ui/react-roving-focus@1.1.11(@types/react-dom@19.1.9(@types/react@19.1.12))(@types/react@19.1.12)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-roving-focus': public
  '@radix-ui/react-use-callback-ref@1.1.1(@types/react@19.1.12)(react@19.1.0)':
    '@radix-ui/react-use-callback-ref': public
  '@radix-ui/react-use-controllable-state@1.2.2(@types/react@19.1.12)(react@19.1.0)':
    '@radix-ui/react-use-controllable-state': public
  '@radix-ui/react-use-effect-event@0.0.2(@types/react@19.1.12)(react@19.1.0)':
    '@radix-ui/react-use-effect-event': public
  '@radix-ui/react-use-escape-keydown@1.1.1(@types/react@19.1.12)(react@19.1.0)':
    '@radix-ui/react-use-escape-keydown': public
  '@radix-ui/react-use-is-hydrated@0.1.0(@types/react@19.1.12)(react@19.1.0)':
    '@radix-ui/react-use-is-hydrated': public
  '@radix-ui/react-use-layout-effect@1.1.1(@types/react@19.1.12)(react@19.1.0)':
    '@radix-ui/react-use-layout-effect': public
  '@radix-ui/react-use-previous@1.1.1(@types/react@19.1.12)(react@19.1.0)':
    '@radix-ui/react-use-previous': public
  '@radix-ui/react-use-rect@1.1.1(@types/react@19.1.12)(react@19.1.0)':
    '@radix-ui/react-use-rect': public
  '@radix-ui/react-use-size@1.1.1(@types/react@19.1.12)(react@19.1.0)':
    '@radix-ui/react-use-size': public
  '@radix-ui/react-visually-hidden@1.2.3(@types/react-dom@19.1.9(@types/react@19.1.12))(@types/react@19.1.12)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-visually-hidden': public
  '@radix-ui/rect@1.1.1':
    '@radix-ui/rect': public
  '@standard-schema/utils@0.3.0':
    '@standard-schema/utils': public
  '@swc/helpers@0.5.15':
    '@swc/helpers': public
  '@tabler/icons@3.34.1':
    '@tabler/icons': public
  '@tailwindcss/node@4.1.13':
    '@tailwindcss/node': public
  '@tailwindcss/oxide-android-arm64@4.1.13':
    '@tailwindcss/oxide-android-arm64': public
  '@tailwindcss/oxide-darwin-arm64@4.1.13':
    '@tailwindcss/oxide-darwin-arm64': public
  '@tailwindcss/oxide-darwin-x64@4.1.13':
    '@tailwindcss/oxide-darwin-x64': public
  '@tailwindcss/oxide-freebsd-x64@4.1.13':
    '@tailwindcss/oxide-freebsd-x64': public
  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.13':
    '@tailwindcss/oxide-linux-arm-gnueabihf': public
  '@tailwindcss/oxide-linux-arm64-gnu@4.1.13':
    '@tailwindcss/oxide-linux-arm64-gnu': public
  '@tailwindcss/oxide-linux-arm64-musl@4.1.13':
    '@tailwindcss/oxide-linux-arm64-musl': public
  '@tailwindcss/oxide-linux-x64-gnu@4.1.13':
    '@tailwindcss/oxide-linux-x64-gnu': public
  '@tailwindcss/oxide-linux-x64-musl@4.1.13':
    '@tailwindcss/oxide-linux-x64-musl': public
  '@tailwindcss/oxide-wasm32-wasi@4.1.13':
    '@tailwindcss/oxide-wasm32-wasi': public
  '@tailwindcss/oxide-win32-arm64-msvc@4.1.13':
    '@tailwindcss/oxide-win32-arm64-msvc': public
  '@tailwindcss/oxide-win32-x64-msvc@4.1.13':
    '@tailwindcss/oxide-win32-x64-msvc': public
  '@tailwindcss/oxide@4.1.13':
    '@tailwindcss/oxide': public
  aria-hidden@1.2.6:
    aria-hidden: public
  caniuse-lite@1.0.30001741:
    caniuse-lite: public
  chownr@3.0.0:
    chownr: public
  client-only@0.0.1:
    client-only: public
  color-convert@2.0.1:
    color-convert: public
  color-name@1.1.4:
    color-name: public
  color-string@1.9.1:
    color-string: public
  color@4.2.3:
    color: public
  csstype@3.1.3:
    csstype: public
  detect-libc@2.0.4:
    detect-libc: public
  detect-node-es@1.1.0:
    detect-node-es: public
  enhanced-resolve@5.18.3:
    enhanced-resolve: public
  get-nonce@1.0.1:
    get-nonce: public
  graceful-fs@4.2.11:
    graceful-fs: public
  is-arrayish@0.3.2:
    is-arrayish: public
  jiti@2.5.1:
    jiti: public
  jose@6.1.0:
    jose: public
  lightningcss-darwin-arm64@1.30.1:
    lightningcss-darwin-arm64: public
  lightningcss-darwin-x64@1.30.1:
    lightningcss-darwin-x64: public
  lightningcss-freebsd-x64@1.30.1:
    lightningcss-freebsd-x64: public
  lightningcss-linux-arm-gnueabihf@1.30.1:
    lightningcss-linux-arm-gnueabihf: public
  lightningcss-linux-arm64-gnu@1.30.1:
    lightningcss-linux-arm64-gnu: public
  lightningcss-linux-arm64-musl@1.30.1:
    lightningcss-linux-arm64-musl: public
  lightningcss-linux-x64-gnu@1.30.1:
    lightningcss-linux-x64-gnu: public
  lightningcss-linux-x64-musl@1.30.1:
    lightningcss-linux-x64-musl: public
  lightningcss-win32-arm64-msvc@1.30.1:
    lightningcss-win32-arm64-msvc: public
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: public
  lightningcss@1.30.1:
    lightningcss: public
  magic-string@0.30.18:
    magic-string: public
  minipass@7.1.2:
    minipass: public
  minizlib@3.0.2:
    minizlib: public
  mkdirp@3.0.1:
    mkdirp: public
  nanoid@3.3.11:
    nanoid: public
  oauth4webapi@3.8.1:
    oauth4webapi: public
  picocolors@1.1.1:
    picocolors: public
  postcss@8.5.6:
    postcss: public
  preact-render-to-string@6.5.11(preact@10.24.3):
    preact-render-to-string: public
  preact@10.24.3:
    preact: public
  react-remove-scroll-bar@2.3.8(@types/react@19.1.12)(react@19.1.0):
    react-remove-scroll-bar: public
  react-remove-scroll@2.7.1(@types/react@19.1.12)(react@19.1.0):
    react-remove-scroll: public
  react-style-singleton@2.2.3(@types/react@19.1.12)(react@19.1.0):
    react-style-singleton: public
  scheduler@0.26.0:
    scheduler: public
  semver@7.7.2:
    semver: public
  sharp@0.34.3:
    sharp: public
  simple-swizzle@0.2.2:
    simple-swizzle: public
  source-map-js@1.2.1:
    source-map-js: public
  styled-jsx@5.1.6(react@19.1.0):
    styled-jsx: public
  tapable@2.2.3:
    tapable: public
  tar@7.4.3:
    tar: public
  tslib@2.8.1:
    tslib: public
  undici-types@6.21.0:
    undici-types: public
  use-callback-ref@1.3.3(@types/react@19.1.12)(react@19.1.0):
    use-callback-ref: public
  use-sidecar@1.1.3(@types/react@19.1.12)(react@19.1.0):
    use-sidecar: public
  use-sync-external-store@1.5.0(react@19.1.0):
    use-sync-external-store: public
  yallist@5.0.0:
    yallist: public
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.0
pendingBuilds: []
prunedAt: Sun, 07 Sep 2025 02:33:20 GMT
publicHoistPattern:
  - '*'
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@biomejs/cli-darwin-arm64@2.2.0'
  - '@biomejs/cli-darwin-x64@2.2.0'
  - '@biomejs/cli-linux-arm64-musl@2.2.0'
  - '@biomejs/cli-linux-arm64@2.2.0'
  - '@biomejs/cli-linux-x64-musl@2.2.0'
  - '@biomejs/cli-linux-x64@2.2.0'
  - '@biomejs/cli-win32-arm64@2.2.0'
  - '@emnapi/runtime@1.5.0'
  - '@img/sharp-darwin-arm64@0.34.3'
  - '@img/sharp-darwin-x64@0.34.3'
  - '@img/sharp-libvips-darwin-arm64@1.2.0'
  - '@img/sharp-libvips-darwin-x64@1.2.0'
  - '@img/sharp-libvips-linux-arm64@1.2.0'
  - '@img/sharp-libvips-linux-arm@1.2.0'
  - '@img/sharp-libvips-linux-ppc64@1.2.0'
  - '@img/sharp-libvips-linux-s390x@1.2.0'
  - '@img/sharp-libvips-linux-x64@1.2.0'
  - '@img/sharp-libvips-linuxmusl-arm64@1.2.0'
  - '@img/sharp-libvips-linuxmusl-x64@1.2.0'
  - '@img/sharp-linux-arm64@0.34.3'
  - '@img/sharp-linux-arm@0.34.3'
  - '@img/sharp-linux-ppc64@0.34.3'
  - '@img/sharp-linux-s390x@0.34.3'
  - '@img/sharp-linux-x64@0.34.3'
  - '@img/sharp-linuxmusl-arm64@0.34.3'
  - '@img/sharp-linuxmusl-x64@0.34.3'
  - '@img/sharp-wasm32@0.34.3'
  - '@img/sharp-win32-arm64@0.34.3'
  - '@img/sharp-win32-ia32@0.34.3'
  - '@next/swc-darwin-arm64@15.5.2'
  - '@next/swc-darwin-x64@15.5.2'
  - '@next/swc-linux-arm64-gnu@15.5.2'
  - '@next/swc-linux-arm64-musl@15.5.2'
  - '@next/swc-linux-x64-gnu@15.5.2'
  - '@next/swc-linux-x64-musl@15.5.2'
  - '@next/swc-win32-arm64-msvc@15.5.2'
  - '@tailwindcss/oxide-android-arm64@4.1.13'
  - '@tailwindcss/oxide-darwin-arm64@4.1.13'
  - '@tailwindcss/oxide-darwin-x64@4.1.13'
  - '@tailwindcss/oxide-freebsd-x64@4.1.13'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.13'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.13'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.13'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.13'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.13'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.13'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.13'
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Documents\Projets\tontine\frontend\node_modules\.pnpm
virtualStoreDirMaxLength: 60
