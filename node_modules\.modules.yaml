hoistPattern:
  - '*'
hoistedDependencies:
  '@biomejs/cli-darwin-arm64@2.2.3':
    '@biomejs/cli-darwin-arm64': private
  '@biomejs/cli-darwin-x64@2.2.3':
    '@biomejs/cli-darwin-x64': private
  '@biomejs/cli-linux-arm64-musl@2.2.3':
    '@biomejs/cli-linux-arm64-musl': private
  '@biomejs/cli-linux-arm64@2.2.3':
    '@biomejs/cli-linux-arm64': private
  '@biomejs/cli-linux-x64-musl@2.2.3':
    '@biomejs/cli-linux-x64-musl': private
  '@biomejs/cli-linux-x64@2.2.3':
    '@biomejs/cli-linux-x64': private
  '@biomejs/cli-win32-arm64@2.2.3':
    '@biomejs/cli-win32-arm64': private
  '@biomejs/cli-win32-x64@2.2.3':
    '@biomejs/cli-win32-x64': private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.0
pendingBuilds: []
prunedAt: Mon, 08 Sep 2025 00:22:44 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@biomejs/cli-darwin-arm64@2.2.3'
  - '@biomejs/cli-darwin-x64@2.2.3'
  - '@biomejs/cli-linux-arm64-musl@2.2.3'
  - '@biomejs/cli-linux-arm64@2.2.3'
  - '@biomejs/cli-linux-x64-musl@2.2.3'
  - '@biomejs/cli-linux-x64@2.2.3'
  - '@biomejs/cli-win32-arm64@2.2.3'
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Documents\Projets\tontine\node_modules\.pnpm
virtualStoreDirMaxLength: 60
