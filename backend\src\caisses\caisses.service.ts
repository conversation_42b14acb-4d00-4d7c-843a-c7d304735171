import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Caisse, CaisseDocument, CaisseType } from './schemas/caisse.schema';
import { CreateCaisseDto } from './dto/create-caisse.dto';
import { UpdateCaisseDto } from './dto/update-caisse.dto';

@Injectable()
export class CaissesService {
  constructor(@InjectModel(Caisse.name) private caisseModel: Model<CaisseDocument>) {}

  async create(dto: CreateCaisseDto, createdBy: string): Promise<Caisse> {
    if (dto.type === CaisseType.REUNION) {
      if (!dto.sessionId) throw new BadRequestException('sessionId requis pour une caisse de type REUNION');
      if (!dto.caissePrincipaleId) throw new BadRequestException('caissePrincipaleId requis pour l\'émargement');
    }

    const caisse = await this.caisseModel.create({
      nom: dto.nom,
      type: dto.type,
      soldeActuel: dto.soldeActuel ?? 0,
      sessionId: dto.sessionId ? new Types.ObjectId(dto.sessionId) : undefined,
      //reunionId: dto.reunionId ? new Types.ObjectId(dto.reunionId) : undefined,
      caissePrincipaleId: dto.caissePrincipaleId ? new Types.ObjectId(dto.caissePrincipaleId) : undefined,
      createdBy: new Types.ObjectId(createdBy),
    });

    return caisse.toObject();
  }

  async findAll(): Promise<Caisse[]> {
    return this.caisseModel.find().sort({ createdAt: -1 }).lean();
  }

  async findOne(id: string): Promise<Caisse | null> {
    if (!Types.ObjectId.isValid(id)) throw new NotFoundException('Caisse introuvable');
    return this.caisseModel.findById(id).lean();
  }

  async update(id: string, dto: UpdateCaisseDto): Promise<Caisse | null> {
    if (!Types.ObjectId.isValid(id)) throw new NotFoundException('Caisse introuvable');
    const update: any = {};
    if (dto.nom !== undefined) update.nom = dto.nom;
    if (dto.type !== undefined) update.type = dto.type;
    if (dto.soldeActuel !== undefined) update.soldeActuel = dto.soldeActuel;
    if (dto.sessionId !== undefined) update.sessionId = dto.sessionId ? new Types.ObjectId(dto.sessionId) : undefined;
    //if (dto.reunionId !== undefined) update.reunionId = dto.reunionId ? new Types.ObjectId(dto.reunionId) : undefined;
    if (dto.caissePrincipaleId !== undefined) update.caissePrincipaleId = dto.caissePrincipaleId ? new Types.ObjectId(dto.caissePrincipaleId) : undefined;

    const updated = await this.caisseModel.findByIdAndUpdate(id, { $set: update }, { new: true });
    return updated?.toObject() ?? null;
  }

  async remove(id: string): Promise<void> {
    if (!Types.ObjectId.isValid(id)) throw new NotFoundException('Caisse introuvable');
    const caisse = await this.caisseModel.findById(id);
    if (!caisse) throw new NotFoundException('Caisse introuvable');

    if (caisse.type === CaisseType.REUNION && caisse.sessionId) {
      throw new BadRequestException('Impossible de supprimer une caisse de réunion liée à une session active');
    }

    await caisse.deleteOne();
  }

  async emargerCaisse(id: string): Promise<Caisse> {
    if (!Types.ObjectId.isValid(id)) throw new NotFoundException('Caisse introuvable');
    const caisse = await this.caisseModel.findById(id);
    if (!caisse) throw new NotFoundException('Caisse introuvable');

    if (caisse.type !== CaisseType.REUNION) {
      throw new BadRequestException("Seules les caisses de type REUNION peuvent être émargées");
    }
    if (!caisse.caissePrincipaleId) {
      throw new BadRequestException('Aucune caisse principale liée pour émargement');
    }

    const principal = await this.caisseModel.findById(caisse.caissePrincipaleId);
    if (!principal) throw new NotFoundException('Caisse principale liée introuvable');
    if (principal.type !== CaisseType.PRINCIPALE) {
      throw new BadRequestException("La caisse liée n'est pas de type PRINCIPALE");
    }

    const montant = caisse.soldeActuel || 0;
    if (montant <= 0) throw new BadRequestException('Solde actuel nul, rien à émarger');

    principal.soldeActuel = (principal.soldeActuel || 0) + montant;
    caisse.soldeActuel = 0;

    await principal.save();
    await caisse.save();

    return caisse.toObject();
  }
}