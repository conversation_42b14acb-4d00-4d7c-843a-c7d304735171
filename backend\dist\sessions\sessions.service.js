"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const session_schema_1 = require("./schemas/session.schema");
const reunion_schema_1 = require("../reunions/schemas/reunion.schema");
let SessionsService = class SessionsService {
    constructor(sessionModel, reunionModel) {
        this.sessionModel = sessionModel;
        this.reunionModel = reunionModel;
    }
    generateSundays(start, end) {
        if (end < start)
            throw new common_1.BadRequestException('dateFin doit être >= dateDebut');
        // Find first Sunday on/after start
        const result = [];
        const date = new Date(start);
        const day = date.getDay(); // 0=Sunday
        const diff = (7 - day) % 7; // days until Sunday
        date.setDate(date.getDate() + diff);
        while (date <= end) {
            result.push(new Date(date));
            date.setDate(date.getDate() + 7);
        }
        return result;
    }
    async create(createDto, createdBy) {
        const { annee, dateDebut, dateFin, partFixe } = createDto;
        const start = new Date(dateDebut);
        const end = new Date(dateFin);
        const session = await this.sessionModel.create({
            annee,
            dateDebut: start,
            dateFin: end,
            partFixe,
            createdBy: new mongoose_2.Types.ObjectId(createdBy),
        });
        const sundays = this.generateSundays(start, end);
        if (sundays.length === 0) {
            await session.deleteOne();
            throw new common_1.BadRequestException('Aucune réunion générée (vérifiez la période)');
        }
        // Create reunions for each Sunday
        const reunionsToCreate = sundays.map((d) => ({
            dateReunion: d,
            sessionId: session._id,
        }));
        await this.reunionModel.insertMany(reunionsToCreate);
        return session.toObject();
    }
    async findAll() {
        return this.sessionModel.find().sort({ dateDebut: 1 }).lean();
    }
    async findOne(id) {
        if (!mongoose_2.Types.ObjectId.isValid(id))
            throw new common_1.NotFoundException('Session introuvable');
        return this.sessionModel.findById(id).lean();
    }
    async update(id, dto) {
        if (!mongoose_2.Types.ObjectId.isValid(id))
            throw new common_1.NotFoundException('Session introuvable');
        const updated = await this.sessionModel.findByIdAndUpdate(id, {
            $set: {
                ...('annee' in dto ? { annee: dto.annee } : {}),
                ...('dateDebut' in dto ? { dateDebut: new Date(dto.dateDebut) } : {}),
                ...('dateFin' in dto ? { dateFin: new Date(dto.dateFin) } : {}),
                ...('partFixe' in dto ? { partFixe: dto.partFixe } : {}),
            },
        }, { new: true });
        return updated?.toObject() ?? null;
    }
    async remove(id) {
        if (!mongoose_2.Types.ObjectId.isValid(id))
            throw new common_1.NotFoundException('Session introuvable');
        // Delete reunions of session, then session
        await this.reunionModel.deleteMany({ sessionId: new mongoose_2.Types.ObjectId(id) });
        await this.sessionModel.findByIdAndDelete(id);
    }
};
exports.SessionsService = SessionsService;
exports.SessionsService = SessionsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(session_schema_1.Session.name)),
    __param(1, (0, mongoose_1.InjectModel)(reunion_schema_1.Reunion.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model])
], SessionsService);
