/* import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Tontine } from './tontine.schema';

@Injectable()
export class TontineService {
  constructor(@InjectModel(Tontine.name) private readonly tontineModel: Model<Tontine>) {}

  async create(createTontineDto: any): Promise<Tontine> {
    const createdTontine = new this.tontineModel(createTontineDto);
    return createdTontine.save();
  }

  async findAll(): Promise<Tontine[]> {
    return this.tontineModel.find().exec();
  }

  async findOne(id: string): Promise<Tontine> {
    return this.tontineModel.findById(id).exec();
  }

  async update(id: string, updateTontineDto: any): Promise<Tontine> {
    return this.tontineModel.findByIdAndUpdate(id, updateTontineDto, { new: true }).exec();
  }

  async remove(id: string): Promise<Tontine> {
    return this.tontineModel.findByIdAndRemove(id).exec();
  }
} */