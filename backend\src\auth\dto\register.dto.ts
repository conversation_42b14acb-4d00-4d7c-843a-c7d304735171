import {
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON>ot<PERSON><PERSON><PERSON>,
	Is<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
} from "class-validator";
import { ApiProperty } from "@nestjs/swagger";
import { UserRole, UserStatus } from "../../users/schemas/user.schema";

export class RegisterDto {
	@ApiProperty({ example: "john_doe", description: "Nom d'utilisateur unique" })
	@IsString()
	@IsNotEmpty()
	username!: string;

	@ApiProperty({
		example: "motdepasse123",
		description: "Mot de passe (minimum 6 caractères)",
	})
	@IsString()
	@MinLength(6)
	password!: string;

	@ApiProperty({ example: "Doe", description: "Nom de famille" })
	@IsString()
	@IsNotEmpty()
	nom!: string;

	@ApiProperty({ example: "John", description: "Prénom" })
	@IsString()
	@IsNotEmpty()
	prenom!: string;

	@ApiProperty({
		example: "<EMAIL>",
		description: "<PERSON>resse email unique",
	})
	@IsEmail()
	@IsNotEmpty()
	email!: string;

	@ApiProperty({ example: "+237123456789", description: "Numéro de téléphone" })
	@IsString()
	@IsNotEmpty()
	telephone!: string;

	@ApiProperty({
		enum: UserRole,
		example: UserRole.MEMBRE,
		description: "Rôle de l'utilisateur",
	})
	@IsEnum(UserRole)
	role!: UserRole;

	@ApiProperty({
		enum: UserStatus,
		example: UserStatus.ACTIF,
		description: "Statut de l'utilisateur",
		required: false,
	})
	@IsOptional()
	@IsEnum(UserStatus)
	statut?: UserStatus;
}
