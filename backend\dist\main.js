"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
require("reflect-metadata");
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const swagger_1 = require("@nestjs/swagger");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    // Active la validation globale des DTOs
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true, // Ignore les propriétés non définies dans le DTO
        forbidNonWhitelisted: true, // Erreur si des propriétés non attendues sont envoyées
        forbidUnknownValues: true, // Erreur si le body est vide ou non conforme
        transform: true, // Transforme le payload en instance du DTO
    }));
    const config = new swagger_1.DocumentBuilder()
        .setTitle("Tontine API")
        .setDescription("API de gestion de tontine")
        .setVersion("1.0")
        .addBearerAuth()
        .build();
    app.enableCors({
        origin: ["http://localhost:3000", "https://myapp.com"],
        methods: "GET,HEAD,PUT,PATCH,POST,DELETE",
        credentials: true,
    });
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup("api", app, document);
    const port = process.env.PORT || 4000;
    await app.listen(port);
    console.log(`Application is running on: http://localhost:${port}`);
}
bootstrap();
