import { Body, Controller, Get, Param, Patch, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../common/guards/role.guard';
import { Roles } from '../common/decorators/roles.decorator';
import { UserRole } from '../users/schemas/user.schema';
import { ReunionsService } from './reunions.service';
import { UpdateReunionDto } from './dto/update-reunion.dto';

@ApiTags('reunions')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('reunions')
export class ReunionsController {
  constructor(private readonly reunionsService: ReunionsService) {}

  @Get()
  @Roles(UserRole.ADMIN, UserRole.TRESORIER, UserRole.MEMBRE)
  findAll() {
    return this.reunionsService.findAll();
  }

  @Get(':id')
  @Roles(UserRole.ADMIN, UserRole.TRESORIER, UserRole.MEMBRE)
  findOne(@Param('id') id: string) {
    return this.reunionsService.findOne(id);
  }

  @Patch(':id')
  @Roles(UserRole.ADMIN)
  update(@Param('id') id: string, @Body() dto: UpdateReunionDto) {
    return this.reunionsService.update(id, dto);
  }
}