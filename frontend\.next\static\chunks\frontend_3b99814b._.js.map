{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card'\n      className={cn(\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-header'\n      className={cn(\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-title'\n      className={cn('leading-none font-semibold', className)}\n      {...props}\n    />\n  );\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-description'\n      className={cn('text-muted-foreground text-sm', className)}\n      {...props}\n    />\n  );\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-action'\n      className={cn(\n        'col-start-2 row-span-2 row-start-1 self-start justify-self-end',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-content'\n      className={cn('px-6', className)}\n      {...props}\n    />\n  );\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-footer'\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\n      {...props}\n    />\n  );\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EACX,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EACX,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EACX,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EAAC,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useSession } from \"next-auth/react\";\nimport {\n\tCard,\n\tCardContent,\n\tCardDescription,\n\tCardHeader,\n\tCardTitle,\n} from \"@/components/ui/card\";\nimport { Users, DollarSign, TrendingUp, Activity } from \"lucide-react\";\n\nconst stats = [\n\t{\n\t\ttitle: \"Total Members\",\n\t\tvalue: \"24\",\n\t\tdescription: \"Active tontine members\",\n\t\ticon: Users,\n\t\tcolor: \"text-blue-600\",\n\t\tbgColor: \"bg-blue-100\",\n\t},\n\t{\n\t\ttitle: \"Total Contributions\",\n\t\tvalue: \"$12,450\",\n\t\tdescription: \"This month\",\n\t\ticon: DollarSign,\n\t\tcolor: \"text-green-600\",\n\t\tbgColor: \"bg-green-100\",\n\t},\n\t{\n\t\ttitle: \"Growth\",\n\t\tvalue: \"+12.5%\",\n\t\tdescription: \"From last month\",\n\t\ticon: TrendingUp,\n\t\tcolor: \"text-purple-600\",\n\t\tbgColor: \"bg-purple-100\",\n\t},\n\t{\n\t\ttitle: \"Active Cycles\",\n\t\tvalue: \"3\",\n\t\tdescription: \"Currently running\",\n\t\ticon: Activity,\n\t\tcolor: \"text-orange-600\",\n\t\tbgColor: \"bg-orange-100\",\n\t},\n];\n\nexport default function DashboardPage() {\n\tconst { data: session } = useSession();\n\n\treturn (\n\t\t<div className=\"space-y-6\">\n\t\t\t{/* Welcome section */}\n\t\t\t<div>\n\t\t\t\t<h1 className=\"text-2xl font-bold text-gray-900\">\n\t\t\t\t\tBienvenue, {session?.user?.name}!\n\t\t\t\t</h1>\n\t\t\t\t<p className=\"text-gray-600 mt-1\">\n\t\t\t\t\tVoici un aperçu de votre tontine aujourd'hui.\n\t\t\t\t</p>\n\t\t\t\t{session?.user && (\n\t\t\t\t\t<div className=\"mt-2 text-sm text-gray-500\">\n\t\t\t\t\t\tConnecté en tant que{\" \"}\n\t\t\t\t\t\t<span className=\"font-medium\">{(session.user as any).role}</span> (\n\t\t\t\t\t\t{(session.user as any).username})\n\t\t\t\t\t</div>\n\t\t\t\t)}\n\t\t\t</div>\n\n\t\t\t{/* Stats grid */}\n\t\t\t<div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n\t\t\t\t{stats.map((stat) => {\n\t\t\t\t\tconst Icon = stat.icon;\n\t\t\t\t\treturn (\n\t\t\t\t\t\t<Card key={stat.title}>\n\t\t\t\t\t\t\t<CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n\t\t\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium text-gray-600\">\n\t\t\t\t\t\t\t\t\t{stat.title}\n\t\t\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t\t\t\t<div className={`p-2 rounded-full ${stat.bgColor}`}>\n\t\t\t\t\t\t\t\t\t<Icon className={`h-4 w-4 ${stat.color}`} />\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</CardHeader>\n\t\t\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t\t\t<div className=\"text-2xl font-bold text-gray-900\">\n\t\t\t\t\t\t\t\t\t{stat.value}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<p className=\"text-xs text-gray-600 mt-1\">{stat.description}</p>\n\t\t\t\t\t\t\t</CardContent>\n\t\t\t\t\t\t</Card>\n\t\t\t\t\t);\n\t\t\t\t})}\n\t\t\t</div>\n\n\t\t\t{/* Recent activity */}\n\t\t\t<div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n\t\t\t\t<Card>\n\t\t\t\t\t<CardHeader>\n\t\t\t\t\t\t<CardTitle>Recent Contributions</CardTitle>\n\t\t\t\t\t\t<CardDescription>\n\t\t\t\t\t\t\tLatest member contributions to the tontine\n\t\t\t\t\t\t</CardDescription>\n\t\t\t\t\t</CardHeader>\n\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t<div className=\"space-y-4\">\n\t\t\t\t\t\t\t{[\n\t\t\t\t\t\t\t\t{ name: \"Alice Johnson\", amount: \"$500\", time: \"2 hours ago\" },\n\t\t\t\t\t\t\t\t{ name: \"Bob Smith\", amount: \"$500\", time: \"5 hours ago\" },\n\t\t\t\t\t\t\t\t{ name: \"Carol Davis\", amount: \"$500\", time: \"1 day ago\" },\n\t\t\t\t\t\t\t\t{ name: \"David Wilson\", amount: \"$500\", time: \"2 days ago\" },\n\t\t\t\t\t\t\t].map((contribution, index) => (\n\t\t\t\t\t\t\t\t<div key={index} className=\"flex items-center justify-between\">\n\t\t\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t\t\t<p className=\"text-sm font-medium text-gray-900\">\n\t\t\t\t\t\t\t\t\t\t\t{contribution.name}\n\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t\t<p className=\"text-xs text-gray-500\">{contribution.time}</p>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t<div className=\"text-sm font-medium text-green-600\">\n\t\t\t\t\t\t\t\t\t\t{contribution.amount}\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t))}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</CardContent>\n\t\t\t\t</Card>\n\n\t\t\t\t<Card>\n\t\t\t\t\t<CardHeader>\n\t\t\t\t\t\t<CardTitle>Upcoming Payouts</CardTitle>\n\t\t\t\t\t\t<CardDescription>Next scheduled member payouts</CardDescription>\n\t\t\t\t\t</CardHeader>\n\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t<div className=\"space-y-4\">\n\t\t\t\t\t\t\t{[\n\t\t\t\t\t\t\t\t{ name: \"Emma Brown\", amount: \"$12,000\", date: \"Jan 15, 2025\" },\n\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\tname: \"Frank Miller\",\n\t\t\t\t\t\t\t\t\tamount: \"$12,000\",\n\t\t\t\t\t\t\t\t\tdate: \"Feb 15, 2025\",\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t{ name: \"Grace Lee\", amount: \"$12,000\", date: \"Mar 15, 2025\" },\n\t\t\t\t\t\t\t].map((payout, index) => (\n\t\t\t\t\t\t\t\t<div key={index} className=\"flex items-center justify-between\">\n\t\t\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t\t\t<p className=\"text-sm font-medium text-gray-900\">\n\t\t\t\t\t\t\t\t\t\t\t{payout.name}\n\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t\t<p className=\"text-xs text-gray-500\">{payout.date}</p>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t<div className=\"text-sm font-medium text-blue-600\">\n\t\t\t\t\t\t\t\t\t\t{payout.amount}\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t))}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</CardContent>\n\t\t\t\t</Card>\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAOA;AAAA;AAAA;AAAA;;;AAVA;;;;AAYA,MAAM,QAAQ;IACb;QACC,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,gTAAK;QACX,OAAO;QACP,SAAS;IACV;IACA;QACC,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,mUAAU;QAChB,OAAO;QACP,SAAS;IACV;IACA;QACC,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,mUAAU;QAChB,OAAO;QACP,SAAS;IACV;IACA;QACC,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,yTAAQ;QACd,OAAO;QACP,SAAS;IACV;CACA;AAEc,SAAS;QAQP;;IAPhB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,8QAAU;IAEpC,qBACC,wUAAC;QAAI,WAAU;;0BAEd,wUAAC;;kCACA,wUAAC;wBAAG,WAAU;;4BAAmC;4BACpC,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,IAAI;4BAAC;;;;;;;kCAEjC,wUAAC;wBAAE,WAAU;kCAAqB;;;;;;oBAGjC,CAAA,oBAAA,8BAAA,QAAS,IAAI,mBACb,wUAAC;wBAAI,WAAU;;4BAA6B;4BACtB;0CACrB,wUAAC;gCAAK,WAAU;0CAAe,AAAC,QAAQ,IAAI,CAAS,IAAI;;;;;;4BAAQ;4BAC/D,QAAQ,IAAI,CAAS,QAAQ;4BAAC;;;;;;;;;;;;;0BAMnC,wUAAC;gBAAI,WAAU;0BACb,MAAM,GAAG,CAAC,CAAC;oBACX,MAAM,OAAO,KAAK,IAAI;oBACtB,qBACC,wUAAC,uJAAI;;0CACJ,wUAAC,6JAAU;gCAAC,WAAU;;kDACrB,wUAAC,4JAAS;wCAAC,WAAU;kDACnB,KAAK,KAAK;;;;;;kDAEZ,wUAAC;wCAAI,WAAW,AAAC,oBAAgC,OAAb,KAAK,OAAO;kDAC/C,cAAA,wUAAC;4CAAK,WAAW,AAAC,WAAqB,OAAX,KAAK,KAAK;;;;;;;;;;;;;;;;;0CAGxC,wUAAC,8JAAW;;kDACX,wUAAC;wCAAI,WAAU;kDACb,KAAK,KAAK;;;;;;kDAEZ,wUAAC;wCAAE,WAAU;kDAA8B,KAAK,WAAW;;;;;;;;;;;;;uBAblD,KAAK,KAAK;;;;;gBAiBvB;;;;;;0BAID,wUAAC;gBAAI,WAAU;;kCACd,wUAAC,uJAAI;;0CACJ,wUAAC,6JAAU;;kDACV,wUAAC,4JAAS;kDAAC;;;;;;kDACX,wUAAC,kKAAe;kDAAC;;;;;;;;;;;;0CAIlB,wUAAC,8JAAW;0CACX,cAAA,wUAAC;oCAAI,WAAU;8CACb;wCACA;4CAAE,MAAM;4CAAiB,QAAQ;4CAAQ,MAAM;wCAAc;wCAC7D;4CAAE,MAAM;4CAAa,QAAQ;4CAAQ,MAAM;wCAAc;wCACzD;4CAAE,MAAM;4CAAe,QAAQ;4CAAQ,MAAM;wCAAY;wCACzD;4CAAE,MAAM;4CAAgB,QAAQ;4CAAQ,MAAM;wCAAa;qCAC3D,CAAC,GAAG,CAAC,CAAC,cAAc,sBACpB,wUAAC;4CAAgB,WAAU;;8DAC1B,wUAAC;;sEACA,wUAAC;4DAAE,WAAU;sEACX,aAAa,IAAI;;;;;;sEAEnB,wUAAC;4DAAE,WAAU;sEAAyB,aAAa,IAAI;;;;;;;;;;;;8DAExD,wUAAC;oDAAI,WAAU;8DACb,aAAa,MAAM;;;;;;;2CARZ;;;;;;;;;;;;;;;;;;;;;kCAgBd,wUAAC,uJAAI;;0CACJ,wUAAC,6JAAU;;kDACV,wUAAC,4JAAS;kDAAC;;;;;;kDACX,wUAAC,kKAAe;kDAAC;;;;;;;;;;;;0CAElB,wUAAC,8JAAW;0CACX,cAAA,wUAAC;oCAAI,WAAU;8CACb;wCACA;4CAAE,MAAM;4CAAc,QAAQ;4CAAW,MAAM;wCAAe;wCAC9D;4CACC,MAAM;4CACN,QAAQ;4CACR,MAAM;wCACP;wCACA;4CAAE,MAAM;4CAAa,QAAQ;4CAAW,MAAM;wCAAe;qCAC7D,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACd,wUAAC;4CAAgB,WAAU;;8DAC1B,wUAAC;;sEACA,wUAAC;4DAAE,WAAU;sEACX,OAAO,IAAI;;;;;;sEAEb,wUAAC;4DAAE,WAAU;sEAAyB,OAAO,IAAI;;;;;;;;;;;;8DAElD,wUAAC;oDAAI,WAAU;8DACb,OAAO,MAAM;;;;;;;2CARN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBlB;GAlHwB;;QACG,8QAAU;;;KADb", "debugId": null}}, {"offset": {"line": 548, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/dollar-sign.js", "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/lucide-react%400.542.0_react%4019.1.0/node_modules/lucide-react/src/icons/dollar-sign.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '12', x2: '12', y1: '2', y2: '22', key: '7eqyqh' }],\n  ['path', { d: 'M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6', key: '1b0p4s' }],\n];\n\n/**\n * @component @name DollarSign\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjIiIHkyPSIyMiIgLz4KICA8cGF0aCBkPSJNMTcgNUg5LjVhMy41IDMuNSAwIDAgMCAwIDdoNWEzLjUgMy41IDAgMCAxIDAgN0g2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/dollar-sign\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst DollarSign = createLucideIcon('dollar-sign', __iconNode);\n\nexport default DollarSign;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,EAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA;YAAK,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA;YAAM,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACjE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAqD,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CACpF;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,KAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iQAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 596, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/trending-up.js", "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/lucide-react%400.542.0_react%4019.1.0/node_modules/lucide-react/src/icons/trending-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 7h6v6', key: 'box55l' }],\n  ['path', { d: 'm22 7-8.5 8.5-5-5L2 17', key: '1t1m79' }],\n];\n\n/**\n * @component @name TrendingUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgN2g2djYiIC8+CiAgPHBhdGggZD0ibTIyIDctOC41IDguNS01LTVMMiAxNyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/trending-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingUp = createLucideIcon('trending-up', __iconNode);\n\nexport default TrendingUp;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAa,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAA0B,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CACzD;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,KAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iQAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 641, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/activity.js", "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/lucide-react%400.542.0_react%4019.1.0/node_modules/lucide-react/src/icons/activity.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2',\n      key: '169zse',\n    },\n  ],\n];\n\n/**\n * @component @name Activity\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjIgMTJoLTIuNDhhMiAyIDAgMCAwLTEuOTMgMS40NmwtMi4zNSA4LjM2YS4yNS4yNSAwIDAgMS0uNDggMEw5LjI0IDIuMThhLjI1LjI1IDAgMCAwLS40OCAwbC0yLjM1IDguMzZBMiAyIDAgMCAxIDQuNDkgMTJIMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/activity\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Activity = createLucideIcon('activity', __iconNode);\n\nexport default Activity;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,QAAA,CAAA,CAAA,KAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iQAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}]}