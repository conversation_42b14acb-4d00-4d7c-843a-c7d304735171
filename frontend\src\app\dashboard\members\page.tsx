"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import {
	Plus,
	Search,
	Filter,
	MoreHorizontal,
	Edit,
	Trash2,
	<PERSON>r<PERSON><PERSON><PERSON>,
	UserX,
} from "lucide-react";
import Link from "next/link";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useApi } from "@/hooks/use-api";

interface Member {
	_id: string;
	username: string;
	nom: string;
	prenom: string;
	email: string;
	telephone: string;
	role: "admin" | "tresorier" | "membre";
	statut: "actif" | "en_attente" | "suspendu";
}

interface MemberStats {
	total: number;
	byRole: Record<string, number>;
	byStatus: Record<string, number>;
	activeByRole: Record<string, number>;
}

export default function MembersPage() {
	const { data: session } = useSession();
	const api = useApi();

	const [members, setMembers] = useState<Member[]>([]);
	const [stats, setStats] = useState<MemberStats | null>(null);
	const [loading, setLoading] = useState(true);
	const [searchTerm, setSearchTerm] = useState("");
	const [roleFilter, setRoleFilter] = useState<string>("all");
	const [statusFilter, setStatusFilter] = useState<string>("all");

	// Vérifier les permissions
	const canManageMembers =
		session?.user &&
		((session.user as any).role === "admin" ||
			(session.user as any).role === "tresorier");
	const canEditMembers =
		session?.user && (session.user as any).role === "admin";

	const loadData = async () => {
		try {
			setLoading(true);

			// Construire les paramètres de requête
			const params = new URLSearchParams();
			if (roleFilter !== "all") params.append("role", roleFilter);
			if (statusFilter !== "all") params.append("statut", statusFilter);
			if (searchTerm) params.append("search", searchTerm);

			const queryString = params.toString();
			const endpoint = `/users${queryString ? `?${queryString}` : ""}`;

			// Charger les membres et les statistiques en parallèle
			const [membersData, statsData] = await Promise.all([
				api.authenticatedRequest<Member[]>(endpoint),
				api.authenticatedRequest<MemberStats>("/users/stats"),
			]);

			setMembers(membersData);
			setStats(statsData);
		} catch (error) {
			console.error("Erreur lors du chargement des données:", error);
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		if (session?.accessToken) {
			loadData();
		}
	}, [session, searchTerm, roleFilter, statusFilter]);

	const handleStatusChange = async (memberId: string, newStatus: string) => {
		try {
			await api.authenticatedRequest(`/users/${memberId}/statut/${newStatus}`, {
				method: "PATCH",
			});

			// Recharger les données
			loadData();
		} catch (error) {
			console.error("Erreur lors du changement de statut:", error);
		}
	};

	const getStatusBadge = (status: string) => {
		const variants = {
			actif: "default",
			en_attente: "secondary",
			suspendu: "destructive",
		} as const;

		return (
			<Badge variant={variants[status as keyof typeof variants] || "secondary"}>
				{status.replace("_", " ")}
			</Badge>
		);
	};

	const getRoleBadge = (role: string) => {
		const variants = {
			admin: "destructive",
			tresorier: "default",
			membre: "secondary",
		} as const;

		return (
			<Badge variant={variants[role as keyof typeof variants] || "secondary"}>
				{role}
			</Badge>
		);
	};

	if (!canManageMembers) {
		return (
			<div className="flex items-center justify-center h-64">
				<div className="text-center">
					<h2 className="text-lg font-semibold text-gray-900">Accès refusé</h2>
					<p className="text-gray-600">
						Vous n'avez pas les permissions pour accéder à cette page.
					</p>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* En-tête */}
			<div className="flex justify-between items-center">
				<div>
					<h1 className="text-2xl font-bold text-gray-900">
						Gestion des Membres
					</h1>
					<p className="text-gray-600">Gérez les membres de votre tontine</p>
				</div>
				{canEditMembers && (
					<Link href="/dashboard/members/new">
						<Button>
							<Plus className="h-4 w-4 mr-2" />
							Nouveau membre
						</Button>
					</Link>
				)}
			</div>

			{/* Statistiques */}
			{stats && (
				<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium text-gray-600">
								Total Membres
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">{stats.total}</div>
						</CardContent>
					</Card>
					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium text-gray-600">
								Membres Actifs
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold text-green-600">
								{stats.byStatus.actif || 0}
							</div>
						</CardContent>
					</Card>
					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium text-gray-600">
								En Attente
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold text-orange-600">
								{stats.byStatus.en_attente || 0}
							</div>
						</CardContent>
					</Card>
					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium text-gray-600">
								Suspendus
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold text-red-600">
								{stats.byStatus.suspendu || 0}
							</div>
						</CardContent>
					</Card>
				</div>
			)}

			{/* Filtres et recherche */}
			<Card>
				<CardHeader>
					<CardTitle>Filtres</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="flex flex-col sm:flex-row gap-4">
						<div className="flex-1">
							<div className="relative">
								<Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
								<Input
									placeholder="Rechercher par nom, prénom, email..."
									value={searchTerm}
									onChange={(e) => setSearchTerm(e.target.value)}
									className="pl-10"
								/>
							</div>
						</div>
						<Select value={roleFilter} onValueChange={setRoleFilter}>
							<SelectTrigger className="w-full sm:w-48">
								<SelectValue placeholder="Filtrer par rôle" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">Tous les rôles</SelectItem>
								<SelectItem value="admin">Admin</SelectItem>
								<SelectItem value="tresorier">Trésorier</SelectItem>
								<SelectItem value="membre">Membre</SelectItem>
							</SelectContent>
						</Select>
						<Select value={statusFilter} onValueChange={setStatusFilter}>
							<SelectTrigger className="w-full sm:w-48">
								<SelectValue placeholder="Filtrer par statut" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">Tous les statuts</SelectItem>
								<SelectItem value="actif">Actif</SelectItem>
								<SelectItem value="en_attente">En attente</SelectItem>
								<SelectItem value="suspendu">Suspendu</SelectItem>
							</SelectContent>
						</Select>
					</div>
				</CardContent>
			</Card>

			{/* Liste des membres */}
			<Card>
				<CardHeader>
					<CardTitle>Liste des Membres</CardTitle>
					<CardDescription>
						{members.length} membre(s) trouvé(s)
					</CardDescription>
				</CardHeader>
				<CardContent>
					{loading ? (
						<div className="flex justify-center py-8">
							<div className="text-gray-500">Chargement...</div>
						</div>
					) : (
						<Table>
							<TableHeader>
								<TableRow>
									<TableHead>Nom</TableHead>
									<TableHead>Email</TableHead>
									<TableHead>Téléphone</TableHead>
									<TableHead>Rôle</TableHead>
									<TableHead>Statut</TableHead>
									{canEditMembers && <TableHead>Actions</TableHead>}
								</TableRow>
							</TableHeader>
							<TableBody>
								{members.map((member) => (
									<TableRow key={member._id}>
										<TableCell>
											<div>
												<div className="font-medium">
													{member.prenom} {member.nom}
												</div>
												<div className="text-sm text-gray-500">
													@{member.username}
												</div>
											</div>
										</TableCell>
										<TableCell>{member.email}</TableCell>
										<TableCell>{member.telephone}</TableCell>
										<TableCell>{getRoleBadge(member.role)}</TableCell>
										<TableCell>{getStatusBadge(member.statut)}</TableCell>
										{canEditMembers && (
											<TableCell>
												<DropdownMenu>
													<DropdownMenuTrigger asChild>
														<Button variant="ghost" className="h-8 w-8 p-0">
															<MoreHorizontal className="h-4 w-4" />
														</Button>
													</DropdownMenuTrigger>
													<DropdownMenuContent align="end">
														<DropdownMenuLabel>Actions</DropdownMenuLabel>
														<DropdownMenuItem asChild>
															<Link
																href={`/dashboard/members/${member._id}/edit`}
															>
																<Edit className="h-4 w-4 mr-2" />
																Modifier
															</Link>
														</DropdownMenuItem>
														<DropdownMenuSeparator />
														{member.statut === "actif" ? (
															<DropdownMenuItem
																onClick={() =>
																	handleStatusChange(member._id, "suspendu")
																}
															>
																<UserX className="h-4 w-4 mr-2" />
																Suspendre
															</DropdownMenuItem>
														) : (
															<DropdownMenuItem
																onClick={() =>
																	handleStatusChange(member._id, "actif")
																}
															>
																<UserCheck className="h-4 w-4 mr-2" />
																Activer
															</DropdownMenuItem>
														)}
													</DropdownMenuContent>
												</DropdownMenu>
											</TableCell>
										)}
									</TableRow>
								))}
							</TableBody>
						</Table>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
