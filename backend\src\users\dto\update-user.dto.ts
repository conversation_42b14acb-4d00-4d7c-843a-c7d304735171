import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { UserRole, UserStatus } from "../schemas/user.schema";
import {
	IsEnum,
	IsNotEmpty,
	IsString,
	MinLength,
	ValidateNested,
	IsEmail,
	IsOptional,
} from "class-validator";
import { Type } from "class-transformer";

export class UpdateUserDto {
	@ApiPropertyOptional({
		example: "john_doe_updated",
		description: "Nom d'utilisateur unique",
	})
	@IsOptional()
	@IsString()
	@IsNotEmpty()
	username?: string;

	@ApiPropertyOptional({
		example: "nouveaumotdepasse123",
		description: "Nouveau mot de passe (minimum 6 caractères)",
	})
	@IsOptional()
	@IsString()
	@MinLength(6)
	password?: string;

	@ApiPropertyOptional({ example: "Dupont", description: "Nom de famille" })
	@IsOptional()
	@IsString()
	@IsNotEmpty()
	nom?: string;

	@ApiPropertyOptional({ example: "Jean", description: "Prénom" })
	@IsOptional()
	@IsString()
	@IsNotEmpty()
	prenom?: string;

	@ApiPropertyOptional({
		example: "<EMAIL>",
		description: "Adresse email unique",
	})
	@IsOptional()
	@IsEmail()
	email?: string;

	@ApiPropertyOptional({
		example: "+237987654321",
		description: "Numéro de téléphone",
	})
	@IsOptional()
	@IsString()
	@IsNotEmpty()
	telephone?: string;

	@ApiPropertyOptional({
		enum: UserRole,
		example: UserRole.TRESORIER,
		description: "Rôle de l'utilisateur",
	})
	@IsOptional()
	@IsEnum(UserRole)
	role?: UserRole;

	@ApiPropertyOptional({
		enum: UserStatus,
		example: UserStatus.SUSPENDU,
		description: "Statut de l'utilisateur",
	})
	@IsOptional()
	@IsEnum(UserStatus)
	statut?: UserStatus;
}

export class UpdateUserWrapperDto {
	@IsString()
	@ApiProperty({ example: "64f8c2b7e1a2b3c4d5e6f7a8" })
	readonly _id!: string;

	@ValidateNested()
	@Type(() => UpdateUserDto)
	@ApiProperty({ type: UpdateUserDto })
	readonly data!: UpdateUserDto;
}
