"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CaissesService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const caisse_schema_1 = require("./schemas/caisse.schema");
let CaissesService = class CaissesService {
    constructor(caisseModel) {
        this.caisseModel = caisseModel;
    }
    async create(dto, createdBy) {
        if (dto.type === caisse_schema_1.CaisseType.REUNION) {
            if (!dto.sessionId)
                throw new common_1.BadRequestException('sessionId requis pour une caisse de type REUNION');
            if (!dto.caissePrincipaleId)
                throw new common_1.BadRequestException('caissePrincipaleId requis pour l\'émargement');
        }
        const caisse = await this.caisseModel.create({
            nom: dto.nom,
            type: dto.type,
            soldeActuel: dto.soldeActuel ?? 0,
            sessionId: dto.sessionId ? new mongoose_2.Types.ObjectId(dto.sessionId) : undefined,
            //reunionId: dto.reunionId ? new Types.ObjectId(dto.reunionId) : undefined,
            caissePrincipaleId: dto.caissePrincipaleId ? new mongoose_2.Types.ObjectId(dto.caissePrincipaleId) : undefined,
            createdBy: new mongoose_2.Types.ObjectId(createdBy),
        });
        return caisse.toObject();
    }
    async findAll() {
        return this.caisseModel.find().sort({ createdAt: -1 }).lean();
    }
    async findOne(id) {
        if (!mongoose_2.Types.ObjectId.isValid(id))
            throw new common_1.NotFoundException('Caisse introuvable');
        return this.caisseModel.findById(id).lean();
    }
    async update(id, dto) {
        if (!mongoose_2.Types.ObjectId.isValid(id))
            throw new common_1.NotFoundException('Caisse introuvable');
        const update = {};
        if (dto.nom !== undefined)
            update.nom = dto.nom;
        if (dto.type !== undefined)
            update.type = dto.type;
        if (dto.soldeActuel !== undefined)
            update.soldeActuel = dto.soldeActuel;
        if (dto.sessionId !== undefined)
            update.sessionId = dto.sessionId ? new mongoose_2.Types.ObjectId(dto.sessionId) : undefined;
        //if (dto.reunionId !== undefined) update.reunionId = dto.reunionId ? new Types.ObjectId(dto.reunionId) : undefined;
        if (dto.caissePrincipaleId !== undefined)
            update.caissePrincipaleId = dto.caissePrincipaleId ? new mongoose_2.Types.ObjectId(dto.caissePrincipaleId) : undefined;
        const updated = await this.caisseModel.findByIdAndUpdate(id, { $set: update }, { new: true });
        return updated?.toObject() ?? null;
    }
    async remove(id) {
        if (!mongoose_2.Types.ObjectId.isValid(id))
            throw new common_1.NotFoundException('Caisse introuvable');
        const caisse = await this.caisseModel.findById(id);
        if (!caisse)
            throw new common_1.NotFoundException('Caisse introuvable');
        if (caisse.type === caisse_schema_1.CaisseType.REUNION && caisse.sessionId) {
            throw new common_1.BadRequestException('Impossible de supprimer une caisse de réunion liée à une session active');
        }
        await caisse.deleteOne();
    }
    async emargerCaisse(id) {
        if (!mongoose_2.Types.ObjectId.isValid(id))
            throw new common_1.NotFoundException('Caisse introuvable');
        const caisse = await this.caisseModel.findById(id);
        if (!caisse)
            throw new common_1.NotFoundException('Caisse introuvable');
        if (caisse.type !== caisse_schema_1.CaisseType.REUNION) {
            throw new common_1.BadRequestException("Seules les caisses de type REUNION peuvent être émargées");
        }
        if (!caisse.caissePrincipaleId) {
            throw new common_1.BadRequestException('Aucune caisse principale liée pour émargement');
        }
        const principal = await this.caisseModel.findById(caisse.caissePrincipaleId);
        if (!principal)
            throw new common_1.NotFoundException('Caisse principale liée introuvable');
        if (principal.type !== caisse_schema_1.CaisseType.PRINCIPALE) {
            throw new common_1.BadRequestException("La caisse liée n'est pas de type PRINCIPALE");
        }
        const montant = caisse.soldeActuel || 0;
        if (montant <= 0)
            throw new common_1.BadRequestException('Solde actuel nul, rien à émarger');
        principal.soldeActuel = (principal.soldeActuel || 0) + montant;
        caisse.soldeActuel = 0;
        await principal.save();
        await caisse.save();
        return caisse.toObject();
    }
};
exports.CaissesService = CaissesService;
exports.CaissesService = CaissesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(caisse_schema_1.Caisse.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], CaissesService);
