{"name": "tontine-nestjs-app", "version": "1.0.0", "description": "A NestJS application for managing a tontine with user authentication.", "main": "dist/main.js", "scripts": {"start": "nest start", "build": "nest build", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "test": "jest"}, "dependencies": {"@nestjs/common": "^11.0.0", "@nestjs/core": "^11.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/mongoose": "^11.0.0", "@nestjs/passport": "^11.0.0", "@nestjs/platform-express": "^11.1.6", "@nestjs/swagger": "^11.2.0", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "mongoose": "^8.0.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "swagger-ui-express": "^4.6.3"}, "devDependencies": {"@nestjs/testing": "^11.0.0", "@types/bcrypt": "^5.0.2", "@types/passport-jwt": "^4.0.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.3.0"}, "keywords": ["<PERSON><PERSON><PERSON>", "tontine", "authentication", "jwt", "mongodb"], "author": "APOLLOS GAMTCHESSI", "license": "MIT"}