"use strict";
/* import { Module } from '@nestjs/common';
import { TontineService } from './tontine.service';
import { TontineController } from './tontine.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Tontine, TontineSchema } from './tontine.schema';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Tontine.name, schema: TontineSchema }]),
  ],
  controllers: [TontineController],
  providers: [TontineService],
})
export class TontineModule {} */ 
