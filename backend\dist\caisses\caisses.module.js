"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CaissesModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const caisses_controller_1 = require("./caisses.controller");
const caisses_service_1 = require("./caisses.service");
const caisse_schema_1 = require("./schemas/caisse.schema");
let CaissesModule = class CaissesModule {
};
exports.CaissesModule = CaissesModule;
exports.CaissesModule = CaissesModule = __decorate([
    (0, common_1.Module)({
        imports: [mongoose_1.MongooseModule.forFeature([{ name: caisse_schema_1.Caisse.name, schema: caisse_schema_1.CaisseSchema }])],
        controllers: [caisses_controller_1.CaissesController],
        providers: [caisses_service_1.CaissesService],
        exports: [caisses_service_1.CaissesService],
    })
], CaissesModule);
