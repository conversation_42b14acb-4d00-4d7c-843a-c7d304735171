// Script de test pour la gestion des membres
const http = require('http');

const API_BASE = 'http://localhost:3000';

// Données de test
const adminUser = {
  username: 'admin_test',
  password: 'admin123',
  nom: 'Admin',
  prenom: 'Super',
  email: '<EMAIL>',
  telephone: '+237111111111',
  role: 'admin',
  statut: 'actif'
};

const testMembers = [
  {
    username: 'membre1',
    password: 'membre123',
    nom: '<PERSON><PERSON>',
    prenom: '<PERSON>',
    email: '<EMAIL>',
    telephone: '+237222222222',
    role: 'membre',
    statut: 'actif'
  },
  {
    username: 'tresorier1',
    password: 'tresorier123',
    nom: '<PERSON>',
    prenom: '<PERSON>',
    email: '<EMAIL>',
    telephone: '+237333333333',
    role: 'tresorier',
    statut: 'actif'
  },
  {
    username: 'membre2',
    password: 'membre123',
    nom: 'Durand',
    prenom: '<PERSON>',
    email: '<EMAIL>',
    telephone: '+237444444444',
    role: 'membre',
    statut: 'en_attente'
  }
];

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsed
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', (e) => {
      reject(e);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testMembersManagement() {
  console.log('🧪 Test de la gestion des membres\n');

  try {
    let adminToken = null;

    // 1. Créer un admin de test
    console.log('1️⃣ Création d\'un admin de test...');
    const registerOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/auth/register',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const adminRegisterResponse = await makeRequest(registerOptions, adminUser);
    if (adminRegisterResponse.statusCode === 201 || adminRegisterResponse.statusCode === 409) {
      console.log('   ✅ Admin créé ou existe déjà');
    } else {
      console.log('   ❌ Erreur création admin:', adminRegisterResponse.data);
      return;
    }

    // 2. Connexion admin
    console.log('\n2️⃣ Connexion admin...');
    const loginOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const loginResponse = await makeRequest(loginOptions, {
      username: adminUser.username,
      password: adminUser.password
    });

    if (loginResponse.statusCode === 200) {
      adminToken = loginResponse.data.access_token;
      console.log('   ✅ Connexion admin réussie');
    } else {
      console.log('   ❌ Erreur connexion admin:', loginResponse.data);
      return;
    }

    // 3. Créer des membres de test
    console.log('\n3️⃣ Création de membres de test...');
    const createUserOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/users',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${adminToken}`
      }
    };

    for (const member of testMembers) {
      const createResponse = await makeRequest(createUserOptions, member);
      if (createResponse.statusCode === 201 || createResponse.statusCode === 409) {
        console.log(`   ✅ Membre ${member.username} créé ou existe déjà`);
      } else {
        console.log(`   ⚠️  Erreur création ${member.username}:`, createResponse.data);
      }
    }

    // 4. Test des statistiques
    console.log('\n4️⃣ Test des statistiques...');
    const statsOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/users/stats',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    };

    const statsResponse = await makeRequest(statsOptions);
    if (statsResponse.statusCode === 200) {
      console.log('   ✅ Statistiques récupérées');
      console.log(`   📊 Total utilisateurs: ${statsResponse.data.total}`);
      console.log(`   👥 Par rôle:`, statsResponse.data.byRole);
      console.log(`   📈 Par statut:`, statsResponse.data.byStatus);
    } else {
      console.log('   ❌ Erreur statistiques:', statsResponse.data);
    }

    // 5. Test filtrage par rôle
    console.log('\n5️⃣ Test filtrage par rôle...');
    const membersOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/users?role=membre',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    };

    const membersResponse = await makeRequest(membersOptions);
    if (membersResponse.statusCode === 200) {
      console.log(`   ✅ Membres trouvés: ${membersResponse.data.length}`);
    } else {
      console.log('   ❌ Erreur filtrage membres:', membersResponse.data);
    }

    // 6. Test recherche
    console.log('\n6️⃣ Test recherche...');
    const searchOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/users?search=Dupont',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    };

    const searchResponse = await makeRequest(searchOptions);
    if (searchResponse.statusCode === 200) {
      console.log(`   ✅ Résultats recherche "Dupont": ${searchResponse.data.length}`);
    } else {
      console.log('   ❌ Erreur recherche:', searchResponse.data);
    }

    // 7. Test changement de statut
    console.log('\n7️⃣ Test changement de statut...');
    if (membersResponse.statusCode === 200 && membersResponse.data.length > 0) {
      const memberId = membersResponse.data[0]._id;
      const statusOptions = {
        hostname: 'localhost',
        port: 3000,
        path: `/users/${memberId}/statut/suspendu`,
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      };

      const statusResponse = await makeRequest(statusOptions);
      if (statusResponse.statusCode === 200) {
        console.log('   ✅ Statut changé avec succès');
        console.log(`   📝 Nouveau statut: ${statusResponse.data.statut}`);
      } else {
        console.log('   ❌ Erreur changement statut:', statusResponse.data);
      }
    }

    console.log('\n🎉 Tests de gestion des membres terminés !');
    
  } catch (error) {
    console.error('❌ Erreur lors des tests:', error.message);
    console.log('\n💡 Assurez-vous que:');
    console.log('   - Le backend est démarré (npm start dans /backend)');
    console.log('   - MongoDB est en cours d\'exécution');
    console.log('   - Le port 3000 est disponible');
  }
}

// Attendre un peu puis lancer les tests
setTimeout(testMembersManagement, 1000);
