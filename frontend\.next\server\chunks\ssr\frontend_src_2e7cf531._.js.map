{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/layout/header.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Head<PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/layout/header.tsx <module evaluation>\",\n    \"Header\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,SAAS,IAAA,mZAAuB,EACzC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/layout/header.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Header() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/layout/header.tsx\",\n    \"Header\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,SAAS,IAAA,mZAAuB,EACzC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,uDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/layout/sidebar.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Sidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/layout/sidebar.tsx <module evaluation>\",\n    \"Sidebar\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,UAAU,IAAA,mZAAuB,EAC1C;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,4EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/layout/sidebar.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Sidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/layout/sidebar.tsx\",\n    \"Sidebar\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,UAAU,IAAA,mZAAuB,EAC1C;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,wDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/app/dashboard/layout.tsx"], "sourcesContent": ["import { Header } from \"@/components/layout/header\";\nimport { Sidebar } from \"@/components/layout/sidebar\";\n\nexport default function DashboardLayout({\n\tchildren,\n}: {\n\tchildren: React.ReactNode;\n}) {\n\treturn (\n\t\t<div className=\"min-h-screen bg-gray-50 flex\">\n\t\t\t<Sidebar />\n\t\t\t<div className=\"flex-1 flex flex-col\">\n\t\t\t\t<Header />\n\t\t\t\t<main className=\"flex-1 p-6\">{children}</main>\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEe,SAAS,gBAAgB,EACvC,QAAQ,EAGR;IACA,qBACC,yXAAC;QAAI,WAAU;;0BACd,yXAAC,8JAAO;;;;;0BACR,yXAAC;gBAAI,WAAU;;kCACd,yXAAC,4JAAM;;;;;kCACP,yXAAC;wBAAK,WAAU;kCAAc;;;;;;;;;;;;;;;;;;AAIlC", "debugId": null}}]}