import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsDateString, IsMongoId, IsOptional, IsString } from 'class-validator';

export class UpdateReunionDto {
  @ApiPropertyOptional({ example: '2025-03-02' })
  @IsOptional()
  @IsDateString()
  dateReunion?: string;

  @ApiPropertyOptional({ example: 'Salle A' })
  @IsOptional()
  @IsString()
  lieu?: string;

  @ApiPropertyOptional({ description: 'ID de la caisse principale associée' })
  @IsOptional()
  @IsMongoId()
  caissePrincipale?: string;
}