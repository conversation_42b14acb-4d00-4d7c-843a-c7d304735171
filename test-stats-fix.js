/**
 * Script de test pour vérifier que l'erreur 500 sur /users/stats est résolue
 */

const API_BASE = 'http://localhost:3000';

async function testStatsEndpoint() {
  console.log('🧪 Test de l\'endpoint /users/stats après correction...\n');

  try {
    // 1. Connexion admin pour obtenir un token
    console.log('1️⃣ Connexion en tant qu\'admin...');
    const loginResponse = await fetch(`${API_BASE}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      })
    });

    if (!loginResponse.ok) {
      console.log('❌ Échec de la connexion admin');
      console.log('ℹ️  Assurez-vous qu\'un utilisateur admin existe avec username: "admin", password: "admin123"');
      return;
    }

    const loginData = await loginResponse.json();
    const token = loginData.access_token;
    console.log('✅ Connexion admin réussie');

    // 2. Test de l'endpoint stats
    console.log('\n2️⃣ Test de l\'endpoint /users/stats...');
    const statsResponse = await fetch(`${API_BASE}/users/stats`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      }
    });

    console.log(`Status: ${statsResponse.status}`);

    if (statsResponse.ok) {
      const statsData = await statsResponse.json();
      console.log('✅ Endpoint /users/stats fonctionne !');
      console.log('📊 Statistiques reçues:');
      console.log(JSON.stringify(statsData, null, 2));
    } else {
      const errorData = await statsResponse.text();
      console.log('❌ Erreur sur /users/stats:');
      console.log(errorData);
    }

    // 3. Test des autres endpoints spécialisés
    console.log('\n3️⃣ Test des autres endpoints...');
    
    // Test by-role
    const roleResponse = await fetch(`${API_BASE}/users/by-role/admin`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    console.log(`/users/by-role/admin: ${roleResponse.status} ${roleResponse.ok ? '✅' : '❌'}`);

    // Test by-statut
    const statutResponse = await fetch(`${API_BASE}/users/by-statut/actif`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    console.log(`/users/by-statut/actif: ${statutResponse.status} ${statutResponse.ok ? '✅' : '❌'}`);

    // 4. Test qu'un ID utilisateur fonctionne toujours
    console.log('\n4️⃣ Test qu\'un vrai ID utilisateur fonctionne...');
    
    // D'abord, récupérer la liste des utilisateurs pour avoir un ID
    const usersResponse = await fetch(`${API_BASE}/users`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    if (usersResponse.ok) {
      const users = await usersResponse.json();
      if (users.length > 0) {
        const userId = users[0]._id;
        const userResponse = await fetch(`${API_BASE}/users/${userId}`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        console.log(`/users/${userId}: ${userResponse.status} ${userResponse.ok ? '✅' : '❌'}`);
      }
    }

  } catch (error) {
    console.error('❌ Erreur lors du test:', error.message);
    console.log('\nℹ️  Assurez-vous que:');
    console.log('   - Le backend est démarré (npm start dans /backend)');
    console.log('   - MongoDB est en cours d\'exécution');
    console.log('   - Un utilisateur admin existe');
  }
}

// Exécuter le test
testStatsEndpoint();
