import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsInt, IsNumber, IsPositive } from 'class-validator';

export class CreateSessionDto {
  @ApiProperty({ example: 2025 })
  @IsInt()
  annee!: number;

  @ApiProperty({ example: '2025-01-01' })
  @IsDateString()
  dateDebut!: string;

  @ApiProperty({ example: '2025-12-31' })
  @IsDateString()
  dateFin!: string;

  @ApiProperty({ example: 1000 })
  @IsNumber()
  @IsPositive()
  partFixe!: number;
}