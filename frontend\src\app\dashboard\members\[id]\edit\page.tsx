"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Arrow<PERSON>ef<PERSON>, Trash2 } from "lucide-react";
import Link from "next/link";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	AlertDialog<PERSON><PERSON>le,
	AlertDialog<PERSON>rigger,
} from "@/components/ui/alert-dialog";
import { useApi } from "@/hooks/use-api";

const editMemberSchema = z.object({
	username: z
		.string()
		.min(3, "Le nom d'utilisateur doit contenir au moins 3 caractères"),
	password: z.string().optional(),
	nom: z.string().min(2, "Le nom doit contenir au moins 2 caractères"),
	prenom: z.string().min(2, "Le prénom doit contenir au moins 2 caractères"),
	email: z.string().email("Adresse email invalide"),
	telephone: z
		.string()
		.min(8, "Le numéro de téléphone doit contenir au moins 8 caractères"),
	role: z.enum(["admin", "tresorier", "membre"]),
	statut: z.enum(["actif", "en_attente", "suspendu"]),
});

type EditMemberForm = z.infer<typeof editMemberSchema>;

interface Member {
	_id: string;
	username: string;
	nom: string;
	prenom: string;
	email: string;
	telephone: string;
	role: "admin" | "tresorier" | "membre";
	statut: "actif" | "en_attente" | "suspendu";
}
export default function EditMemberPage() {
	const { id: memberId } = useParams();
	const { data: session } = useSession();
	const router = useRouter();
	const api = useApi();

	const [member, setMember] = useState<Member | null>(null);
	const [isLoading, setIsLoading] = useState(false);
	const [isDeleting, setIsDeleting] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [loading, setLoading] = useState(true);

	// Vérifier les permissions
	const canEditMembers =
		session?.user && (session.user as any).role === "admin";

	const form = useForm<EditMemberForm>({
		resolver: zodResolver(editMemberSchema),
		defaultValues: {
			username: "",
			password: "",
			nom: "",
			prenom: "",
			email: "",
			telephone: "",
			role: "membre",
			statut: "actif",
		},
	});

	useEffect(() => {
		if (session?.accessToken && memberId) {
			loadMember();
		}
	}, [session, memberId]);

	const loadMember = async () => {
		try {
			setLoading(true);
			const memberData = await api.authenticatedRequest<Member>(
				`/users/${memberId}`,
			);
			setMember(memberData);

			// Remplir le formulaire avec les données existantes
			form.reset({
				username: memberData.username,
				password: "", // Ne pas pré-remplir le mot de passe
				nom: memberData.nom,
				prenom: memberData.prenom,
				email: memberData.email,
				telephone: memberData.telephone,
				role: memberData.role,
				statut: memberData.statut,
			});
		} catch (error) {
			console.error("Erreur lors du chargement du membre:", error);
			setError("Membre non trouvé");
		} finally {
			setLoading(false);
		}
	};

	const onSubmit = async (data: EditMemberForm) => {
		setIsLoading(true);
		setError(null);

		try {
			// Supprimer le mot de passe du payload s'il est vide
			const updateData = { ...data };
			if (!updateData.password) {
				delete updateData.password;
			}

			await api.authenticatedRequest(`/users/${memberId}`, {
				method: "PATCH",

				body: JSON.stringify(updateData),
			});

			// Rediriger vers la liste des membres
			router.push("/dashboard/members");
		} catch (error) {
			console.error("Erreur lors de la mise à jour du membre:", error);
			if (error instanceof Error) {
				setError(error.message);
			} else {
				setError(
					"Erreur lors de la mise à jour du membre. Veuillez réessayer.",
				);
			}
		} finally {
			setIsLoading(false);
		}
	};

	const handleDelete = async () => {
		setIsDeleting(true);
		try {
			await api.authenticatedRequest(`/users/${memberId}`, {
				method: "DELETE",
			});
			router.push("/dashboard/members");
		} catch (error) {
			console.error("Erreur lors de la suppression:", error);
			setError("Erreur lors de la suppression du membre");
		} finally {
			setIsDeleting(false);
		}
	};

	if (!canEditMembers) {
		return (
			<div className="space-y-6">
				<div className="flex items-center gap-4">
					<Link href="/dashboard/members">
						<Button variant="ghost" size="sm">
							<ArrowLeft className="h-4 w-4 mr-2" />
							Retour
						</Button>
					</Link>
				</div>
				<div className="flex items-center justify-center h-64">
					<div className="text-center">
						<h2 className="text-lg font-semibold text-gray-900">
							Accès refusé
						</h2>
						<p className="text-gray-600">
							Seuls les administrateurs peuvent modifier des membres.
						</p>
					</div>
				</div>
			</div>
		);
	}

	if (loading) {
		return (
			<div className="space-y-6">
				<div className="flex items-center gap-4">
					<Link href="/dashboard/members">
						<Button variant="ghost" size="sm">
							<ArrowLeft className="h-4 w-4 mr-2" />
							Retour
						</Button>
					</Link>
				</div>
				<div className="flex justify-center py-8">
					<div className="text-gray-500">Chargement...</div>
				</div>
			</div>
		);
	}

	if (error && !member) {
		return (
			<div className="space-y-6">
				<div className="flex items-center gap-4">
					<Link href="/dashboard/members">
						<Button variant="ghost" size="sm">
							<ArrowLeft className="h-4 w-4 mr-2" />
							Retour
						</Button>
					</Link>
				</div>
				<div className="flex items-center justify-center h-64">
					<div className="text-center">
						<h2 className="text-lg font-semibold text-gray-900">Erreur</h2>
						<p className="text-gray-600">{error}</p>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* En-tête */}
			<div className="flex items-center justify-between">
				<div className="flex items-center gap-4">
					<Link href="/dashboard/members">
						<Button variant="ghost" size="sm">
							<ArrowLeft className="h-4 w-4 mr-2" />
							Retour
						</Button>
					</Link>
					<div>
						<h1 className="text-2xl font-bold text-gray-900">
							Modifier {member?.prenom} {member?.nom}
						</h1>
						<p className="text-gray-600">Modifier les informations du membre</p>
					</div>
				</div>

				{/* Bouton de suppression */}
				<AlertDialog>
					<AlertDialogTrigger asChild>
						<Button variant="destructive" size="sm">
							<Trash2 className="h-4 w-4 mr-2" />
							Supprimer
						</Button>
					</AlertDialogTrigger>
					<AlertDialogContent>
						<AlertDialogHeader>
							<AlertDialogTitle>Confirmer la suppression</AlertDialogTitle>
							<AlertDialogDescription>
								Êtes-vous sûr de vouloir supprimer ce membre ? Cette action est
								irréversible.
							</AlertDialogDescription>
						</AlertDialogHeader>
						<AlertDialogFooter>
							<AlertDialogCancel>Annuler</AlertDialogCancel>
							<AlertDialogAction
								onClick={handleDelete}
								disabled={isDeleting}
								className="bg-red-600 hover:bg-red-700"
							>
								{isDeleting ? "Suppression..." : "Supprimer"}
							</AlertDialogAction>
						</AlertDialogFooter>
					</AlertDialogContent>
				</AlertDialog>
			</div>

			{/* Formulaire */}
			<Card className="max-w-2xl">
				<CardHeader>
					<CardTitle>Informations du membre</CardTitle>
					<CardDescription>Modifiez les informations du membre</CardDescription>
				</CardHeader>
				<CardContent>
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
							{/* Informations personnelles */}
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<FormField
									control={form.control}
									name="prenom"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Prénom *</FormLabel>
											<FormControl>
												<Input
													placeholder="Prénom"
													{...field}
													disabled={isLoading}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name="nom"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Nom *</FormLabel>
											<FormControl>
												<Input
													placeholder="Nom de famille"
													{...field}
													disabled={isLoading}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							{/* Informations de contact */}
							<FormField
								control={form.control}
								name="email"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Email *</FormLabel>
										<FormControl>
											<Input
												type="email"
												placeholder="<EMAIL>"
												{...field}
												disabled={isLoading}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="telephone"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Téléphone *</FormLabel>
										<FormControl>
											<Input
												placeholder="+237123456789"
												{...field}
												disabled={isLoading}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Informations de connexion */}
							<div className="border-t pt-6">
								<h3 className="text-lg font-medium mb-4">
									Informations de connexion
								</h3>

								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<FormField
										control={form.control}
										name="username"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Nom d'utilisateur *</FormLabel>
												<FormControl>
													<Input
														placeholder="nom_utilisateur"
														{...field}
														disabled={isLoading}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="password"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Nouveau mot de passe</FormLabel>
												<FormControl>
													<Input
														type="password"
														placeholder="Laisser vide pour ne pas changer"
														{...field}
														disabled={isLoading}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							</div>

							{/* Rôle et statut */}
							<div className="border-t pt-6">
								<h3 className="text-lg font-medium mb-4">Rôle et statut</h3>

								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<FormField
										control={form.control}
										name="role"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Rôle *</FormLabel>
												<Select
													onValueChange={field.onChange}
													value={field.value}
												>
													<FormControl>
														<SelectTrigger disabled={isLoading}>
															<SelectValue />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														<SelectItem value="membre">Membre</SelectItem>
														<SelectItem value="tresorier">Trésorier</SelectItem>
														<SelectItem value="admin">
															Administrateur
														</SelectItem>
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="statut"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Statut *</FormLabel>
												<Select
													onValueChange={field.onChange}
													value={field.value}
												>
													<FormControl>
														<SelectTrigger disabled={isLoading}>
															<SelectValue />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														<SelectItem value="actif">Actif</SelectItem>
														<SelectItem value="en_attente">
															En attente
														</SelectItem>
														<SelectItem value="suspendu">Suspendu</SelectItem>
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							</div>

							{/* Message d'erreur */}
							{error && (
								<div className="text-red-600 text-sm bg-red-50 p-3 rounded">
									{error}
								</div>
							)}

							{/* Actions */}
							<div className="flex justify-end gap-4 pt-6">
								<Link href="/dashboard/members">
									<Button variant="outline" disabled={isLoading}>
										Annuler
									</Button>
								</Link>
								<Button type="submit" disabled={isLoading}>
									{isLoading ? "Mise à jour..." : "Mettre à jour"}
								</Button>
							</div>
						</form>
					</Form>
				</CardContent>
			</Card>
		</div>
	);
}
