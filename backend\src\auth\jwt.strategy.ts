
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { UsersService } from '../users/users.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private readonly usersService: UsersService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: 'temporaire', // process.env.JWT_SECRET
    });
  }

  async validate(payload: any) {
    const user = await this.usersService.findById(payload.sub);
    console.log('Payload JWT:', user);
    if (!user) {
      throw new UnauthorizedException();
    }
    // Validate and return payload data to be put in request.user
    return { userId: payload.sub, username: payload.username, role: payload.role };
  }
}